import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifyInstructorSession } from '@/app/lib/instructor-session';

// GET: Fetch dashboard stats for the current instructor
export async function GET(req: NextRequest) {
  const user = await verifyInstructorSession();
  if (!user) {
    return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }
  try {
    // Total Exams created by instructor
    const totalExams = await prisma.exam.count({ where: { instructorId: user.id } });
    // Active Students: students currently connected to an ongoing exam by this instructor
    const activeStudents = await prisma.examSession.count({
      where: {
        exam: { instructorId: user.id },
        status: 'active',
      },
    });
    // Completion Rate: percentage of students who completed exams out of all who started
    const completed = await prisma.examSession.count({
      where: {
        exam: { instructorId: user.id },
        status: 'completed',
      },
    });
    const totalSessions = await prisma.examSession.count({
      where: {
        exam: { instructorId: user.id },
      },
    });
    const completionRate = totalSessions > 0 ? (completed / totalSessions) * 100 : 0;
    // Average Score: average score for all completed exam sessions
    const avgScoreAgg = await prisma.examSession.aggregate({
      where: {
        exam: { instructorId: user.id },
        status: 'completed',
      },
      _avg: { score: true },
    });
    const averageScore = avgScoreAgg._avg.score || 0;
    return NextResponse.json({
      success: true,
      stats: {
        totalExams,
        activeStudents,
        completionRate: Number(completionRate.toFixed(1)),
        averageScore: Number(averageScore.toFixed(1)),
      },
    });
  } catch (error) {
    return NextResponse.json({ success: false, error: 'Failed to fetch stats' }, { status: 500 });
  }
}
