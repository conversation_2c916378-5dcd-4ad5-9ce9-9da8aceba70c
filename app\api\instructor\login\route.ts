import prisma from "@/app/lib/prisma"
import { NextResponse } from "next/server"
import bcrypt from 'bcrypt'
import { createInstructorSession } from "@/app/lib/instructor-session"

export async function POST(request:Request){
    const {email,password,univID} = await request.json()

    const User = await prisma.instructor.findUnique({where:{
        email,
        univ:{
            some:{
                id:univID,
            }
        }
    },select:{id:true,password:true}})

    if(!User){
        return NextResponse.json({error:'Invalid credentials'},{status:400})
    }

    const verifyPassword = await bcrypt.compare(password,User.password) ? true : password == User.password ? true : false

    if(!verifyPassword){
        return NextResponse.json({error:'Invalid credentials'},{status:400})
    }

    await createInstructorSession({id:User.id,email,role:'instructor'})
    return NextResponse.json({message:'Login Succesfully',role:'instructor'},{status:200})

}