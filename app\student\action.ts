'use server'
import prisma from "../lib/prisma"

export async function checkAndStartExamSessions() {
  const now = new Date()

  // Fetch exams starting within the last 5 minutes but without sessions
  const examsToStart = await prisma.exam.findMany({
    where: {
      startDate: {
        lte: now
      },
      sessions: {
        none: {}
      }
    },
    include: {
      enrollments: {
        where: {
          status: 'approved'
        },
        include: {
          student: true
        }
      }
    }
  })

  for (const exam of examsToStart) {
    const session = await prisma.examSession.create({
      data: {
        examId: exam.id,
        status: 'active',
        startedAt: now,
        students: {
          create: exam.enrollments.map(enroll => ({
            studentId: enroll.studentId
          }))
        }
      }
    })

    console.log(`Created session ${session.id} for exam ${exam.title}`)
  }
}

interface EnrollParams {
  studentId: string
  examId: string
}

export async function enrollStudent({ studentId, examId }: EnrollParams) {
  try {
    // Check if already enrolled
    const existing = await prisma.examEnrollment.findUnique({
      where: {
        studentId_examId: { studentId, examId }
      }
    })

    if (existing) {
      return {
        success: false,
        message: 'You are already enrolled in this exam.'
      }
    }

    await prisma.examEnrollment.create({
      data: {
        studentId,
        examId,
        status: 'pending'
      }
    })

    
    return {
      success: true,
      message: 'Enrollment successful.'
    }

  } catch (error) {
    console.error('Enrollment failed:', error)
    return {
      success: false,
      message: 'Enrollment failed. Try again later.'
    }
  }
}

interface AnswerPayload {
  questionId: string
  answer: string | string[] // adapt if storing as Json or String
}
export async function saveStudentExamAnswers(
  studentId: string,
  examSessionId: string,
  answers: AnswerPayload[],
  timeSpent: number // in seconds, optional
) {
  try {
    // Create or update answers in bulk
    const savePromises = answers.map(async (a) => {
      return prisma.answers.upsert({
        where: {
          questionId_studentId_examSessionId: {
            questionId: a.questionId,
            studentId,
            examSessionId
          }
        },
        update: {
          // Update fields as needed
          // example: value: a.answer,
          answer: Array.isArray(a.answer) ? a.answer : [a.answer], // Convert array to string if needed
          updatedAt: new Date()
        },
        create: {
          questionId: a.questionId,
          studentId,
          examSessionId,
          answer: Array.isArray(a.answer) ? a.answer : [a.answer], // Convert array to string if needed
          createdAt: new Date(),
        }
      })
    })

    const savedAnswers = await Promise.all(savePromises)

    // Optionally update exam session status to COMPLETED
    await prisma.studentExamSession.update({
      where: {
        studentId_examSessionId: {
          studentId,
          examSessionId,
        }
      },
      data: {
        status: "COMPLETED",
        timeTaken: timeSpent, // Store time spent in seconds
        endTime: new Date()
      }
    })

    return { success: true, savedAnswers }

  } catch (error) {
    console.error("Failed to save exam answers:", error)
    return { success: false, error: "Failed to save exam answers" }
  }
}