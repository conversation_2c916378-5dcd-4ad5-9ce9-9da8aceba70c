import { Checkbox } from "@/components/ui/checkbox"

export function IntegrationTable() {
  return (
    <div className="overflow-hidden">
      <table className="w-full">
        <thead>
          <tr className="border-b text-left text-xs font-medium text-gray-500">
            <th className="pb-2"></th>
            <th className="pb-2">APPLICATION</th>
            <th className="pb-2">TYPE</th>
            <th className="pb-2">RATE</th>
            <th className="pb-2 text-right">PROFIT</th>
          </tr>
        </thead>
        <tbody>
          <tr className="border-b">
            <td className="py-3 pr-2">
              <Checkbox id="stripe" />
            </td>
            <td className="py-3">
              <div className="flex items-center gap-2">
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-purple-600 text-white">
                  <span className="text-xs">S</span>
                </div>
                <span className="text-sm">Stripe</span>
              </div>
            </td>
            <td className="py-3 text-sm">Finance</td>
            <td className="py-3">
              <div className="h-2 w-12 rounded bg-purple-200">
                <div className="h-2 w-5 rounded bg-purple-600"></div>
              </div>
            </td>
            <td className="py-3 text-right text-sm">$650.00</td>
          </tr>
          <tr className="border-b">
            <td className="py-3 pr-2">
              <Checkbox id="zapier" />
            </td>
            <td className="py-3">
              <div className="flex items-center gap-2">
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-orange-500 text-white">
                  <span className="text-xs">Z</span>
                </div>
                <span className="text-sm">Zapier</span>
              </div>
            </td>
            <td className="py-3 text-sm">CRM</td>
            <td className="py-3">
              <div className="h-2 w-12 rounded bg-purple-200">
                <div className="h-2 w-7 rounded bg-purple-600"></div>
              </div>
            </td>
            <td className="py-3 text-right text-sm">$720.50</td>
          </tr>
          <tr>
            <td className="py-3 pr-2">
              <Checkbox id="shopify" />
            </td>
            <td className="py-3">
              <div className="flex items-center gap-2">
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-white">
                  <span className="text-xs">S</span>
                </div>
                <span className="text-sm">Shopify</span>
              </div>
            </td>
            <td className="py-3 text-sm">Marketplace</td>
            <td className="py-3">
              <div className="h-2 w-12 rounded bg-purple-200">
                <div className="h-2 w-3 rounded bg-purple-600"></div>
              </div>
            </td>
            <td className="py-3 text-right text-sm">$432.25</td>
          </tr>
        </tbody>
      </table>
    </div>
  )
}
