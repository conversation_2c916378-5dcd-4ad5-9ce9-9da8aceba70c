import prisma from "@/app/lib/prisma";
import { createStudentSession } from "@/app/lib/student-session";
import { NextResponse } from "next/server";
import bcrypt from "bcrypt";

export async function POST(req: Request, context: { params: Promise<{ univId: string }> }) {
  try{
    const { univId } = await context.params;
    const { email, password } = await req.json();
    
    if (!email || !password) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Check if university exists
    const university = await prisma.univ.findUnique({ where: { id: univId } });
    if (!university) {
      return NextResponse.json({ error: "University not found" }, { status: 404 });
    }

    // Find student by email
    const student = await prisma.student.findUnique({ where: { email } });
    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, student.password);
    if (!isValidPassword) {
      return NextResponse.json({ error: "Invalid credentials" }, { status: 401 });
    }

    // Create session
    await createStudentSession({
      id: student.id,
      email: student.email,
      role: "student",
      university: { id: univId },
    });

    return NextResponse.json({
      success: true,
      student: { id: student.id, name: student.name, email: student.email },
    });
  }catch (e: any) {
    return NextResponse.json({ error: "Login failed", details: e.message }, { status: 500 });
  }
  
}