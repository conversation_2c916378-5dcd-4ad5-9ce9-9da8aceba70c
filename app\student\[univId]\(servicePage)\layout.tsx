import React from 'react'
import <PERSON>Bar from './_component/SideBar'
import Header from './_component/Header'
import { verifyStudentSession } from '@/app/lib/student-session'
import { redirect } from 'next/navigation'
import { checkAndStartExamSessions } from '../../action'

const layout = async ({children,params}: {children: React.ReactNode,params:Promise<{univId:string}>}) => {
  // verify if the user is logged in
  const {univId} = await params
  const session = await verifyStudentSession()
  if(!session) {
    // redirect to login page if not logged in
    redirect('/student/' + univId + '/login')
  }
  await checkAndStartExamSessions()
  return (
    <div className='flex  h-screen bg-gray-50'>
        <div className="overflow-y-auto overflow-x-hidden ">
            <SideBar />
        </div>
        <div className="w-full flex-1 flex flex-col">
          <Header/>
          {children}</div>
    </div>
  )
}

export default layout