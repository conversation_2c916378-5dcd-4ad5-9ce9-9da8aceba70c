import { NextResponse } from "next/server"
import prisma from "@/app/lib/prisma"

// GET /api/teacher/[teacherId]/courses
export async function GET(
  req: Request,
  { params: { teacherId } }: { params: { teacherId: string } }
) {
  try {
    const courses = await prisma.course.findMany({
      where: { instructorId: teacherId },
      select: { id: true, name: true },
    })
    return NextResponse.json({ courses })
  } catch (e: any) {
    return NextResponse.json({ error: "Failed to fetch courses", details: e.message }, { status: 500 })
  }
}
