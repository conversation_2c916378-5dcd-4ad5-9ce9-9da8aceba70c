version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: smartonline_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: smartonline
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - smartonline_network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: smartonline_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - smartonline_network

  # SmartOnline Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: smartonline_app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/smartonline
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    networks:
      - smartonline_network
    volumes:
      - ./uploads:/app/uploads

  # Nginx Reverse Proxy (optional for production)
  nginx:
    image: nginx:alpine
    container_name: smartonline_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - smartonline_network

volumes:
  postgres_data:
  redis_data:

networks:
  smartonline_network:
    driver: bridge
