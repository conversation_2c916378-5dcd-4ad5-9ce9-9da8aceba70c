import React from 'react'
import SignupPage from './Form'
import prisma from '@/app/lib/prisma'

const page = async ({params}:{params:Promise<{univId:string}>}) => {
    const {univId} = await params
    const universityName = await prisma.univ.findUnique({where:{id:univId},select:{name:true}})
  return (
    <SignupPage univID={univId} univName={universityName?.name || 'Ict university'} />
  )
}

export default page
