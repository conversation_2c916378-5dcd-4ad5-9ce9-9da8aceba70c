"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { ArrowLeft, Key, Save, Building2, BookOpen } from "lucide-react"

export default function CreateTokenPage({univId}:{univId:string}) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [departments, setDepartments] = useState([])
  const [courses, setCourses] = useState([])
  const [selectedDepartment, setSelectedDepartment] = useState("")
  const [formData, setFormData] = useState({
    departmentId: "",
    courseId: "",
    maxUsage: "50",
    expirationDate: "",
    notes: ""
  })

  // Pre-select course if coming from course page
  useEffect(() => {
    const courseId = searchParams.get('course')
    if (courseId) {
      setFormData(prev => ({ ...prev, courseId }))
    }
  }, [searchParams])

  // Fetch departments on component mount
  useEffect(() => {
    fetchDepartments()
  }, [])

  // Fetch courses when department changes
  useEffect(() => {
    if (selectedDepartment) {
      fetchCourses(selectedDepartment)
    } else {
      setCourses([])
    }
  }, [selectedDepartment])

  const fetchDepartments = async () => {
    try {
      const response = await fetch(`/api/admin/${univId}/departments`)
      const result = await response.json()
      if (result.success) {
        setDepartments(result.departments)
      }
    } catch (error) {
      console.error('Failed to fetch departments:', error)
    }
  }

  const fetchCourses = async (departmentId: string) => {
    try {
      const response = await fetch(`/api/admin/${univId}/departments/${departmentId}/courses`)
      const result = await response.json()
      if (result.success) {
        setCourses(result.courses)
      }
    } catch (error) {
      console.error('Failed to fetch courses:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    if (name === 'departmentId') {
      setSelectedDepartment(value)
      setFormData(prev => ({ ...prev, courseId: "" })) // Reset course selection
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/admin/${univId}/tokens`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          maxUsage: parseInt(formData.maxUsage)
        })
      })

      const result = await response.json()

      if (result.success) {
        router.push(`/admin/${univId}/tokens`)
      } else {
        alert(result.error || 'Failed to create token')
      }
    } catch (error) {
      console.error('Submission error:', error)
      alert('Failed to create token. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link 
                href={`/admin/${univId}/tokens`}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Tokens
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-lg font-semibold text-gray-900">Create Instructor Token</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg border border-gray-200 p-8">
          <div className="flex items-center gap-3 mb-8">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
              <Key className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Generate New Instructor Token</h2>
              <p className="text-gray-600">Create access tokens for instructors to manage courses and exams</p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Department Selection */}
            <div>
              <label htmlFor="departmentId" className="block text-sm font-medium text-gray-700 mb-2">
                Department *
              </label>
              <div className="relative">
                <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <select
                  id="departmentId"
                  name="departmentId"
                  required
                  value={formData.departmentId}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Department</option>
                  {departments.map((dept: any) => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name} ({dept.code})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Course Selection */}
            <div>
              <label htmlFor="courseId" className="block text-sm font-medium text-gray-700 mb-2">
                Course (Optional)
              </label>
              <div className="relative">
                <BookOpen className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <select
                  id="courseId"
                  name="courseId"
                  value={formData.courseId}
                  onChange={handleInputChange}
                  disabled={!selectedDepartment}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                >
                  <option value="">All courses in department</option>
                  {courses.map((course: any) => (
                    <option key={course.id} value={course.id}>
                      {course.name} ({course.code})
                    </option>
                  ))}
                </select>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Leave empty to allow access to all courses in the department
              </p>
            </div>

            {/* Token Settings */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Token Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="maxUsage" className="block text-sm font-medium text-gray-700 mb-2">
                    Maximum Usage Count *
                  </label>
                  <input
                    type="number"
                    id="maxUsage"
                    name="maxUsage"
                    required
                    min="1"
                    max="1000"
                    value={formData.maxUsage}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="50"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Number of times this token can be used
                  </p>
                </div>

                <div>
                  <label htmlFor="expirationDate" className="block text-sm font-medium text-gray-700 mb-2">
                    Expiration Date *
                  </label>
                  <input
                    type="date"
                    id="expirationDate"
                    name="expirationDate"
                    required
                    min={new Date().toISOString().split('T')[0]}
                    value={formData.expirationDate}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                Notes (Optional)
              </label>
              <textarea
                id="notes"
                name="notes"
                rows={3}
                value={formData.notes}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Additional notes about this token..."
              />
            </div>

            {/* Action Buttons */}
            <div className="border-t border-gray-200 pt-6 flex items-center justify-end gap-4">
              <Link
                href={`/admin/${univId}/tokens`}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSubmitting}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                  isSubmitting
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    Generate Token
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </main>
    </div>
  )
}
