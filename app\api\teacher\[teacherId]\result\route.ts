import { NextResponse } from "next/server"
import prisma from "@/app/lib/prisma"
import { verifyInstructorSession } from "@/app/lib/instructor-session"

export async function GET(request:Request,{params}:{params:{teacherId:string}}) {
  const {teacherId} = params
  
  // Query ExamSession where examId exists and exam.instructorId matches
  const sessions = await prisma.examSession.findMany({
    include: {
      exam: true,
      students: true,
    },
    where: {
      examId: { not: "" },
      exam: { instructorId: teacherId },
    },
    orderBy: { startedAt: "desc" },
    take: 50, // limit for demo
  })

  // Map DB fields to mockResults shape
  const results = sessions.map((s) => ({
    id: s.id,
    examTitle: s.exam && "title" in s.exam ? s.exam.title : "",
    studentName: s.student && "name" in s.student ? s.student.name : "",
    submissionDate: s.endedAt ? s.endedAt.toISOString() : s.startedAt.toISOString(),
    score: s.score ?? null,
    totalPoints: 100, // Placeholder, adjust if you have this in Exam
    status: s.status === "completed" ? "graded" : "pending",
    course: "", // Placeholder, adjust if you have course info
    timeSpent: s.endedAt && s.startedAt ? Math.round((s.endedAt.getTime() - s.startedAt.getTime()) / 60000) : null,
    flagged: false, // Placeholder, adjust if you have flag info
  }))

  return NextResponse.json({results})
}
