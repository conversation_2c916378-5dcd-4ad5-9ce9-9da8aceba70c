"use client"
import { useState } from 'react'
import {
  ChartBarIcon,
  ClipboardListIcon,
  ArrowUpIcon,
  EyeIcon,
  DownloadIcon,
  ClockIcon
} from 'lucide-react'

interface Topic {
  name: string
  score: number
}

interface Result {
  id: number
  title: string
  score: number
  passingScore: number
  date: string
  feedback: string
  topics: Topic[]
  percentile: number
  timeSpent: string
  status: 'passed' | 'failed'
}

const Results = ({results}:{results:Result[]}) => {
  const [activeTab, setActiveTab] = useState<'all' | 'passed' | 'failed'>('all')
  const [expandedResult, setExpandedResult] = useState<number | null>(null)

  

  const filteredResults =
    activeTab === 'all'
      ? results
      : results.filter(r => r.status === activeTab)

  const toggleExpand = (id: number) => {
    setExpandedResult(expandedResult === id ? null : id)
  }

  const downloadResult = (result: Result) => {
    alert(`Downloading results for ${result.title}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-green-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Your Exam Results</h1>
          <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm font-medium text-gray-700">
            <ArrowUpIcon className="w-4 h-4" />
            Refresh
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b mb-6">
          {['all', 'passed', 'failed'].map(tab => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab as 'all' | 'passed' | 'failed')}
              className={`px-5 py-2 text-sm font-medium border-b-2 ${
                activeTab === tab
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-green-500'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)} Results
            </button>
          ))}
        </div>

        {/* Results */}
        <div className="space-y-4">
          {filteredResults.length > 0 ? (
            filteredResults.map(result => (
              <div
                key={result.id}
                className={`bg-white border-l-4 rounded-lg shadow-sm ${
                  result.status === 'passed'
                    ? 'border-green-500'
                    : 'border-red-500'
                }`}
              >
                <div
                  className="grid grid-cols-3 p-4 cursor-pointer hover:bg-gray-50"
                  onClick={() => toggleExpand(result.id)}
                >
                  <div>
                    <h2 className="text-lg font-semibold text-gray-800">
                      {result.title}
                    </h2>
                    <p className="text-sm text-gray-500">
                      {new Date(result.date).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex flex-col items-center justify-center">
                    <div
                      className={`w-14 h-14 rounded-full flex items-center justify-center font-semibold text-lg border-4 ${
                        result.status === 'passed'
                          ? 'bg-green-100 text-green-700 border-green-300'
                          : 'bg-red-100 text-red-700 border-red-300'
                      }`}
                    >
                      {result.score}%
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Passing: {result.passingScore}%
                    </p>
                  </div>
                  <div className="flex flex-col justify-center gap-1">
                    <p className="flex items-center text-sm text-gray-600">
                      <ChartBarIcon className="w-4 h-4 mr-1" />
                      Top {result.percentile}%
                    </p>
                    <p className="flex items-center text-sm text-gray-600">
                      <ClockIcon className="w-4 h-4 mr-1" />
                      {result.timeSpent}
                    </p>
                  </div>
                </div>

                {expandedResult === result.id && (
                  <div className="border-t px-6 py-4 space-y-4">
                    {/* Topic Breakdown */}
                    <div>
                      <h3 className="flex items-center text-base font-semibold text-gray-700 mb-2">
                        <ClipboardListIcon className="w-5 h-5 mr-2 text-indigo-500" />
                        Topic Breakdown
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {result.topics.map((topic, i) => (
                          <div key={i}>
                            <p className="text-sm font-medium text-gray-700">
                              {topic.name}
                            </p>
                            <div className="relative bg-gray-100 h-3 rounded">
                              <div
                                className="absolute top-0 left-0 h-full bg-indigo-500 rounded"
                                style={{ width: `${topic.score}%` }}
                              />
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              {topic.score}%
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>

                    
                    

                    {/* Actions */}
                    <div className="flex gap-4">
                      <button className="flex items-center gap-1 px-4 py-2 text-sm bg-blue-100 text-green-700 rounded hover:bg-blue-200">
                        <EyeIcon className="w-4 h-4" />
                        View Exam
                      </button>
                      <button
                        onClick={() => downloadResult(result)}
                        className="flex items-center gap-1 px-4 py-2 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200"
                      >
                        <DownloadIcon className="w-4 h-4" />
                        Download Results
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))
          ) : (
            <p className="text-center text-sm text-gray-500">
              No results match your current filters
            </p>
          )}
        </div>
      </div>
    </div>
  )
}

export default Results
