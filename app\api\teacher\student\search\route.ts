import { NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifyInstructorSession } from '@/app/lib/instructor-session';

// GET: Search students by name, email, or other criteria
export async function GET(request: Request) {
  try {
    const instructor = await verifyInstructorSession();
    if (!instructor) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const status = searchParams.get('status') || '';
    const limit = parseInt(searchParams.get('limit') || '20');
    const includeInactive = searchParams.get('includeInactive') === 'true';

    if (!query.trim()) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    // Build where clause
    const whereClause: any = {
      instructor: { some: { id: instructor.id } },
      OR: [
        { name: { contains: query, mode: 'insensitive' } },
        { email: { contains: query, mode: 'insensitive' } }
      ]
    };

    if (status) {
      whereClause.status = status;
    } else if (!includeInactive) {
      whereClause.status = 'active';
    }

    const students = await prisma.student.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        email: true,
        status: true,
        averageScore: true,
        examsCompleted: true,
        lastLogin: true,
        joinDate: true,
        examSessions: {
          select: {
            score: true,
            status: true,
            startedAt: true,
            exam: {
              select: { title: true }
            }
          },
          orderBy: { startedAt: 'desc' },
          take: 3
        }
      },
      orderBy: [
        { status: 'asc' }, // Active students first
        { name: 'asc' }
      ],
      take: limit
    });

    // Add search relevance scoring
    const studentsWithRelevance = students.map(student => {
      let relevanceScore = 0;
      
      // Exact name match gets highest score
      if (student.name.toLowerCase() === query.toLowerCase()) {
        relevanceScore += 100;
      } else if (student.name.toLowerCase().startsWith(query.toLowerCase())) {
        relevanceScore += 80;
      } else if (student.name.toLowerCase().includes(query.toLowerCase())) {
        relevanceScore += 60;
      }
      
      // Email matches
      if (student.email.toLowerCase() === query.toLowerCase()) {
        relevanceScore += 90;
      } else if (student.email.toLowerCase().startsWith(query.toLowerCase())) {
        relevanceScore += 70;
      } else if (student.email.toLowerCase().includes(query.toLowerCase())) {
        relevanceScore += 50;
      }

      // Calculate performance summary
      const completedExams = student.examSessions.filter(session => session.status === 'completed');
      const recentScores = completedExams.map(session => session.score || 0);
      const averageRecentScore = recentScores.length > 0 
        ? recentScores.reduce((a, b) => a + b, 0) / recentScores.length 
        : 0;

      return {
        ...student,
        relevanceScore,
        recentPerformance: {
          averageScore: Math.round(averageRecentScore * 100) / 100,
          recentExamsCount: completedExams.length,
          lastExamDate: completedExams.length > 0 ? completedExams[0].startedAt : null
        }
      };
    });

    // Sort by relevance score
    studentsWithRelevance.sort((a, b) => b.relevanceScore - a.relevanceScore);

    return NextResponse.json({
      query,
      results: studentsWithRelevance,
      totalFound: studentsWithRelevance.length,
      searchMetadata: {
        includeInactive,
        statusFilter: status,
        limit
      }
    });

  } catch (error) {
    console.error('Error searching students:', error);
    return NextResponse.json(
      { error: 'Failed to search students' },
      { status: 500 }
    );
  }
}

// POST: Advanced search with multiple criteria
export async function POST(request: Request) {
  try {
    const instructor = await verifyInstructorSession();
    if (!instructor) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      name,
      email,
      status,
      minScore,
      maxScore,
      minExams,
      maxExams,
      joinDateFrom,
      joinDateTo,
      lastLoginFrom,
      lastLoginTo,
      limit = 50
    } = body;

    // Build complex where clause
    const whereClause: any = {
      instructor: { some: { id: instructor.id } }
    };

    const andConditions = [];

    if (name) {
      andConditions.push({
        name: { contains: name, mode: 'insensitive' }
      });
    }

    if (email) {
      andConditions.push({
        email: { contains: email, mode: 'insensitive' }
      });
    }

    if (status) {
      andConditions.push({ status });
    }

    if (minScore !== undefined || maxScore !== undefined) {
      const scoreCondition: any = {};
      if (minScore !== undefined) scoreCondition.gte = minScore;
      if (maxScore !== undefined) scoreCondition.lte = maxScore;
      andConditions.push({ averageScore: scoreCondition });
    }

    if (minExams !== undefined || maxExams !== undefined) {
      const examCondition: any = {};
      if (minExams !== undefined) examCondition.gte = minExams;
      if (maxExams !== undefined) examCondition.lte = maxExams;
      andConditions.push({ examsCompleted: examCondition });
    }

    if (joinDateFrom || joinDateTo) {
      const dateCondition: any = {};
      if (joinDateFrom) dateCondition.gte = new Date(joinDateFrom);
      if (joinDateTo) dateCondition.lte = new Date(joinDateTo);
      andConditions.push({ joinDate: dateCondition });
    }

    if (lastLoginFrom || lastLoginTo) {
      const loginCondition: any = {};
      if (lastLoginFrom) loginCondition.gte = new Date(lastLoginFrom);
      if (lastLoginTo) loginCondition.lte = new Date(lastLoginTo);
      andConditions.push({ lastLogin: loginCondition });
    }

    if (andConditions.length > 0) {
      whereClause.AND = andConditions;
    }

    const students = await prisma.student.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        email: true,
        status: true,
        averageScore: true,
        examsCompleted: true,
        lastLogin: true,
        joinDate: true,
        createdAt: true,
        examSessions: {
          select: {
            score: true,
            status: true,
            startedAt: true,
            exam: {
              select: { title: true }
            }
          },
          orderBy: { startedAt: 'desc' },
          take: 5
        }
      },
      orderBy: [
        { averageScore: 'desc' },
        { name: 'asc' }
      ],
      take: limit
    });

    // Calculate additional metrics for each student
    const studentsWithMetrics = students.map(student => {
      const completedExams = student.examSessions.filter(session => session.status === 'completed');
      const scores = completedExams.map(session => session.score || 0);
      
      // Calculate performance trend
      let performanceTrend = 'stable';
      if (scores.length >= 3) {
        const firstHalf = scores.slice(0, Math.ceil(scores.length / 2));
        const secondHalf = scores.slice(Math.ceil(scores.length / 2));
        const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
        
        if (secondAvg > firstAvg + 5) performanceTrend = 'improving';
        else if (secondAvg < firstAvg - 5) performanceTrend = 'declining';
      }

      // Calculate activity level
      const daysSinceLastLogin = student.lastLogin 
        ? Math.floor((Date.now() - new Date(student.lastLogin).getTime()) / (1000 * 60 * 60 * 24))
        : null;
      
      let activityLevel = 'unknown';
      if (daysSinceLastLogin !== null) {
        if (daysSinceLastLogin <= 7) activityLevel = 'active';
        else if (daysSinceLastLogin <= 30) activityLevel = 'moderate';
        else activityLevel = 'inactive';
      }

      return {
        ...student,
        performanceTrend,
        activityLevel,
        daysSinceLastLogin,
        recentExamsCount: completedExams.length
      };
    });

    return NextResponse.json({
      searchCriteria: body,
      results: studentsWithMetrics,
      totalFound: studentsWithMetrics.length,
      summary: {
        averageScore: studentsWithMetrics.reduce((sum, s) => sum + (s.averageScore || 0), 0) / studentsWithMetrics.length || 0,
        activeStudents: studentsWithMetrics.filter(s => s.status === 'active').length,
        highPerformers: studentsWithMetrics.filter(s => (s.averageScore || 0) >= 80).length,
        atRiskStudents: studentsWithMetrics.filter(s => (s.averageScore || 0) < 60).length
      }
    });

  } catch (error) {
    console.error('Error in advanced search:', error);
    return NextResponse.json(
      { error: 'Failed to perform advanced search' },
      { status: 500 }
    );
  }
}
