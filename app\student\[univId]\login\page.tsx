import React from 'react'
import Form from './Form'
import LoginPage from './LeftForm'
import prisma from '@/app/lib/prisma'
import { redirect } from 'next/navigation'
import { verifyStudentSession } from '@/app/lib/student-session'

const page = async ({params}:{params:Promise<{univId:string}>}) => {
    const {univId} = await params
    const session = await verifyStudentSession()
    if(session){
      redirect(`/student/${univId}/dashboard`)
    }
    const universityData = await prisma.univ.findUnique({where:{id:univId},select:{name:true}})
    
  return (
    <LoginPage univID={univId} univName={universityData?.name || "Ict university"} />
  )
}

export default page
