import { NextResponse } from "next/server"
import prisma from "@/app/lib/prisma"
import { createStudentSession } from "@/app/lib/student-session"
import bcrypt from "bcrypt"

// POST /api/student/[univId]/signup
export async function POST(req: Request, context: { params: Promise<{ univId: string }> }) {
  try {
    const { univId } = await context.params
    const { name, email, password } = await req.json()
    if (!name || !email || !password) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }
    // Check if university exists
    const university = await prisma.univ.findUnique({ where: { id: univId } })
    if (!university) {
      return NextResponse.json({ error: "University not found" }, { status: 404 })
    }
    //check if the email of the student is a valid to school domain email
    const emailDomain = email.split("@")[1]
    if (emailDomain !== university.domain) {
      return NextResponse.json({ error: "Invalid email domain" }, { status: 400 })
    }
    // Check if student already exists
    const existing = await prisma.student.findUnique({ where: { email } })
    if (existing) {
      return NextResponse.json({ error: "Student already exists" }, { status: 409 })
    }
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10)
    // Create student
    const student = await prisma.student.create({
      data: {
        name,
        email,
        password: hashedPassword,
        univId,
        status: "active",
        lastLogin: new Date(),
      },
    })
    // Create session
    await createStudentSession({ id: student.id, email: student.email, role: "student", university: { id: univId } })
    return NextResponse.json({ success: true, student: { id: student.id, name: student.name, email: student.email } })
  } catch (e: any) {
    return NextResponse.json({ error: "Signup failed", details: e.message }, { status: 500 })
  }
}
