import { NextRequest, NextResponse } from 'next/server'
import { formatDuration, intervalToDuration } from 'date-fns'
import prisma from '@/app/lib/prisma'

export async function GET(req: Request, context: { params: Promise<{ univId: string, studentId: string }> }) {
    const { univId, studentId } = await context.params
  if (!studentId || !univId) {
    return NextResponse.json({ error: 'Missing studentId or univId' }, { status: 400 })
  }

  try {
    // Fetch all courses in the university that have exams and that this student has taken
    const courses = await prisma.course.findMany({
      where: {
        department: {
          univId
        },
        exams: {
          some: {
            sessions: {
              some: {
                students: {
                  some: { studentId }
                }
              }
            }
          }
        }
      },
      include: {
        exams: {
          include: {
            sessions: {
              include: {
                students: {
                  where: { studentId }
                }
              }
            }
          }
        }
      }
    })

    const results = courses.map((course, index) => {
      const exams = course.exams
      const topics = exams.map(exam => ({
        id: exam.id,
        title: exam.title
      }))

      const studentSessions = exams.flatMap(exam => 
        exam.sessions.flatMap(session => session.students)
      )

      const grades = studentSessions.map(s => s.grade).filter(g => g !== null) as number[]

      const score = grades.length > 0 
        ? Math.round(grades.reduce((a, b) => a + b, 0) / grades.length)
        : 0

      const passingScore = 70
      const status = score >= passingScore ? 'passed' : 'failed'

      const lastExamDate = exams.length > 0
        ? exams.map(e => e.startDate).sort((a, b) => b.getTime() - a.getTime())[0]
        : new Date()

      const totalTime = exams.reduce((acc, exam) => acc + exam.duration, 0)
      const timeSpent = formatDuration(intervalToDuration({ start: 0, end: totalTime * 60 * 1000 }))

      return {
        id: index + 1,
        title: course.name,
        score,
        passingScore,
        date: lastExamDate.toISOString(),
        feedback: '',
        topics,
        percentile: 0, // Can compute later
        timeSpent,
        status
      }
    })

    return NextResponse.json(results)
  } catch (error) {
    console.error('[RESULTS_API]', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
