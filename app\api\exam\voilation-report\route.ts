// app/api/exam/violation-report/route.ts
import prisma from '@/app/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';


export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      studentName,
      studentId,
      examId,
      examTitle,
      violationType,
      timestamp,
      examDuration,
      totalViolations,
      violationPoints,
      allViolations,
      userAgent,
      screenResolution,
      blockedAt
    } = body;

    // Validate required fields
    if (!studentName || !studentId || !examId || !violationType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create violation report in database
    const violationReport = await prisma.examViolationReport.create({
      data: {
        studentName,
        studentId,
        examId,
        examTitle: examTitle || 'Unknown Exam',
        violationType,
        timestamp: new Date(timestamp),
        examDuration,
        totalViolations,
        violationPoints,
        allViolations: JSON.stringify(allViolations),
        userAgent,
        screenResolution,
        blockedAt: new Date(blockedAt),
        reportedAt: new Date(),
        status: 'BLOCKED',
        // Add IP address for additional tracking
        ipAddress: request.url || 
                   request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown'
      }
    });

    // Optional: Create individual violation records for detailed analysis
    const violationRecords = await Promise.all(
      allViolations.map((violation: any, index: number) => 
        prisma.examViolation.create({
          data: {
            reportId: violationReport.id,
            violationType: violation.type,
            timestamp: new Date(violation.time),
            severity: violation.severity,
            sequenceNumber: index + 1
          }
        })
      )
    );

    // Optional: Update student's exam status
    await prisma.studentExamSession.upsert({
      where: {
        studentId_examSessionId: {
          studentId,
          examSessionId: examId // Assuming examId is the session ID
        }
      },
      update: {
        status: 'BLOCKED',
        endTime: new Date(blockedAt),
        violationReportId: violationReport.id
      },
      create: {
        examSessionId: examId,
        studentId,
        startTime: new Date(Date.now() - examDuration * 1000),
        endTime: new Date(blockedAt),
        status: 'BLOCKED',
        violationReportId: violationReport.id
      }
    });

    // Optional: Send notification to administrators
    // You can implement email/SMS notification here
    // await sendAdminNotification(violationReport);

    return NextResponse.json({
      success: true,
      reportId: violationReport.id,
      message: 'Violation report created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating violation report:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// Optional: GET endpoint to retrieve violation reports for admin dashboard
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const examId = url.searchParams.get('examId');
    const studentId = url.searchParams.get('studentId');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    const where: any = {};
    if (examId) where.examId = examId;
    if (studentId) where.studentId = studentId;

    const reports = await prisma.examViolationReport.findMany({
      where,
      include: {
        violations: {
          orderBy: { sequenceNumber: 'asc' }
        },
        examSession: true
      },
      orderBy: { reportedAt: 'desc' },
      take: limit,
      skip: offset
    });

    const totalCount = await prisma.examViolationReport.count({ where });

    return NextResponse.json({
      reports,
      totalCount,
      hasMore: offset + limit < totalCount
    });

  } catch (error) {
    console.error('Error fetching violation reports:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
  }
}