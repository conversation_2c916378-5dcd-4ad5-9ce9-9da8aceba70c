import { verifyStudentSession } from "@/app/lib/student-session";
import { redirect } from "next/navigation";
import StudentEnrollmentTable from "./Enrollements";
import { verifyInstructorSession } from "@/app/lib/instructor-session";

const getEnrollments = async (teacherId: string) => {
  const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/teacher/${teacherId}/enrollments`);
  if (!res.ok) {
    throw new Error('Failed to fetch enrollments');
  }

  const enrollments = await res.json();
  console.log('Enrollments:', enrollments);
  return Array.isArray(enrollments) ? enrollments : [];
}


const page = async ({ params }: { params: Promise<{ univId: string }> }) => {
  const { univId } = await params;
  const session = await verifyInstructorSession();
  if (!session) {
    redirect('/teacher/' + univId + '/login');
  }

  const enrollments = await getEnrollments( session.id);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Exam Enrollments</h1>
      <StudentEnrollmentTable Enrollments={enrollments} />
    </div>
  );
};

export default page;