import { GET, POST } from '../monitoring/route';
import { MonitoringService } from '@/lib/monitoring-service';
import { NextRequest } from 'next/server';

// Mock the MonitoringService
jest.mock('@/lib/monitoring-service');

describe('/api/monitoring', () => {
  let mockMonitoringService: jest.Mocked<MonitoringService>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock MonitoringService.getInstance()
    mockMonitoringService = {
      checkSystemHealth: jest.fn(),
      getMetrics: jest.fn(),
    } as any;
    
    (MonitoringService.getInstance as jest.Mock).mockReturnValue(mockMonitoringService);
  });

  describe('GET /api/monitoring', () => {
    it('should return monitoring metrics successfully', async () => {
      const mockMetrics = {
        health: {
          overall: 'healthy',
          timestamp: '2024-01-20T10:30:00.000Z',
          services: [
            {
              name: 'database',
              status: 'healthy',
              lastChecked: '2024-01-20T10:30:00.000Z',
              responseTime: 45,
              details: {
                userCount: 150,
                paymentCount: 89,
                connectionPool: 'active',
              },
            },
          ],
          uptime: 86400000,
          version: '1.0.0',
        },
        metrics: {
          uptime: 86400000,
          serviceFailures: {
            database: 0,
            email: 1,
            payment_gateway: 0,
            external_deps: 0,
          },
          alertConfig: {
            enabled: true,
            recipientCount: 2,
            thresholds: {
              responseTime: 5000,
              errorRate: 10,
              consecutiveFailures: 3,
            },
          },
        },
      };

      mockMonitoringService.getMetrics.mockResolvedValue(mockMetrics);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockMetrics);
      expect(mockMonitoringService.getMetrics).toHaveBeenCalledTimes(1);
    });

    it('should handle monitoring service errors', async () => {
      const error = new Error('Monitoring service failed');
      mockMonitoringService.getMetrics.mockRejectedValue(error);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toEqual({
        error: 'Failed to get monitoring data',
        details: 'Monitoring service failed',
        timestamp: expect.any(String),
      });
    });
  });

  describe('POST /api/monitoring', () => {
    it('should handle health-check action', async () => {
      const mockHealthData = {
        overall: 'healthy',
        timestamp: '2024-01-20T10:30:00.000Z',
        services: [
          {
            name: 'database',
            status: 'healthy',
            lastChecked: '2024-01-20T10:30:00.000Z',
            responseTime: 45,
          },
        ],
        uptime: 86400000,
        version: '1.0.0',
      };

      mockMonitoringService.checkSystemHealth.mockResolvedValue(mockHealthData);

      const request = new NextRequest('http://localhost:3000/api/monitoring', {
        method: 'POST',
        body: JSON.stringify({ action: 'health-check' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockHealthData);
      expect(mockMonitoringService.checkSystemHealth).toHaveBeenCalledTimes(1);
    });

    it('should handle test-alerts action in development', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const request = new NextRequest('http://localhost:3000/api/monitoring', {
        method: 'POST',
        body: JSON.stringify({ action: 'test-alerts' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        success: true,
        message: 'Test alert triggered',
        alert: expect.objectContaining({
          severity: 'warning',
          service: 'test_service',
          status: 'degraded',
          error: 'This is a test alert',
          responseTime: 5500,
          timestamp: expect.any(String),
          environment: 'development',
        }),
      });

      process.env.NODE_ENV = originalEnv;
    });

    it('should reject test-alerts action in production', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const request = new NextRequest('http://localhost:3000/api/monitoring', {
        method: 'POST',
        body: JSON.stringify({ action: 'test-alerts' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data).toEqual({
        error: 'Test alerts not available in production',
      });

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle invalid action', async () => {
      const request = new NextRequest('http://localhost:3000/api/monitoring', {
        method: 'POST',
        body: JSON.stringify({ action: 'invalid-action' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toEqual({
        error: 'Invalid action. Use "health-check" or "test-alerts"',
      });
    });

    it('should handle malformed request body', async () => {
      const request = new NextRequest('http://localhost:3000/api/monitoring', {
        method: 'POST',
        body: 'invalid-json',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toEqual({
        error: 'Monitoring action failed',
        details: expect.any(String),
      });
    });

    it('should handle monitoring service errors in POST', async () => {
      const error = new Error('Health check failed');
      mockMonitoringService.checkSystemHealth.mockRejectedValue(error);

      const request = new NextRequest('http://localhost:3000/api/monitoring', {
        method: 'POST',
        body: JSON.stringify({ action: 'health-check' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toEqual({
        error: 'Monitoring action failed',
        details: 'Health check failed',
      });
    });
  });
});
