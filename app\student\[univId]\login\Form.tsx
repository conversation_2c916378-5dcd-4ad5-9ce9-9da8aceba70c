'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import LoginPage from './LeftForm';
 
export default function Form({univID}:{univID:string}) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    if (!email || !password) {
      setError('Please enter both email and password.');
      setLoading(false);
      return;
    }

    try {
      const res = await fetch('/api/auth/student/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password,univID }),
      });

      const data = await res.json();

      if (!res.ok) {
        setError(data.error || 'Login failed');
      } else {
        if(data.role == "admin") router.push(`/univ/${univID}/ad/dashboard`) 
        if(data.role == "student") router.push(`/univ/${univID}/st/dashboard`) 
      }
    } catch {
      setError('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left side - Login form */}
      <LoginPage univID='i' univName='i'/>

      {/* Right side - Hero section */}
      <div className="flex-1 relative bg-gray-100 ">
        <div className="absolute inset-0 bg-black/40" />
        <div className="absolute top-0 translate-y-4/5 left-0 right-0 p-12 text-white">
          <h2 className="text-4xl font-bold mb-4 leading-tight space-y-20">
            Smart Exam Management <br />
            Designed for You
          </h2>
          <p className="text-lg mb-8 opacity-90 max-w-md">
            Seamless login, secure dashboard, and all your academic tools in one place.
          </p>
        </div>
      </div>
    </div>
  );
}
