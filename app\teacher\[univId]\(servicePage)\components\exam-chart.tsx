"use client"

import { useEffect, useRef } from "react"

export function Exam<PERSON>hart() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    // Data for exam performance
    const exams = ["Midterm 1", "Quiz 1", "Midterm 2", "Quiz 2", "Final"]
    const data = [
      { exam: "Midterm 1", avgScore: 82, participation: 95 },
      { exam: "Quiz 1", avgScore: 88, participation: 98 },
      { exam: "Midterm 2", avgScore: 75, participation: 92 },
      { exam: "Quiz 2", avgScore: 91, participation: 96 },
      { exam: "Final", avgScore: 78, participation: 89 },
    ]

    // Chart dimensions
    const chartWidth = canvas.width - 60
    const chartHeight = canvas.height - 60
    const barGroupWidth = chartWidth / exams.length
    const barWidth = barGroupWidth * 0.3

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Draw bars
    data.forEach((examData, index) => {
      const x = index * barGroupWidth + 30

      // Average Score bar
      const avgScoreHeight = (examData.avgScore / 100) * chartHeight
      const avgScoreY = canvas.height - avgScoreHeight - 30
      ctx.fillStyle = "#3b82f6"
      ctx.fillRect(x, avgScoreY, barWidth, avgScoreHeight)

      // Participation bar
      const participationHeight = (examData.participation / 100) * chartHeight
      const participationY = canvas.height - participationHeight - 30
      ctx.fillStyle = "#10b981"
      ctx.fillRect(x + barWidth + 5, participationY, barWidth, participationHeight)

      // Exam labels
      ctx.fillStyle = "#6b7280"
      ctx.font = "11px Inter, sans-serif"
      ctx.textAlign = "center"
      ctx.fillText(examData.exam, x + barWidth, canvas.height - 10)
    })

    // Draw legend
    ctx.fillStyle = "#3b82f6"
    ctx.fillRect(20, 10, 12, 12)
    ctx.fillStyle = "#374151"
    ctx.font = "12px Inter, sans-serif"
    ctx.textAlign = "left"
    ctx.fillText("Average Score", 38, 21)

    ctx.fillStyle = "#10b981"
    ctx.fillRect(140, 10, 12, 12)
    ctx.fillText("Participation", 158, 21)
  }, [])

  return (
    <div className="h-[200px] w-full">
      <canvas ref={canvasRef} className="h-full w-full"></canvas>
    </div>
  )
}
