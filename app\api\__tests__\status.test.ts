import { GET } from '../status/route';
import { MonitoringService } from '@/lib/monitoring-service';
import { PrismaClient } from '@prisma/client';

// Mock dependencies
jest.mock('@/lib/monitoring-service');
jest.mock('@prisma/client');

describe('/api/status', () => {
  let mockMonitoringService: jest.Mocked<MonitoringService>;
  let mockPrisma: jest.Mocked<PrismaClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock MonitoringService
    mockMonitoringService = {
      checkSystemHealth: jest.fn(),
    } as any;
    (MonitoringService.getInstance as jest.Mock).mockReturnValue(mockMonitoringService);

    // Mock Prisma
    mockPrisma = new PrismaClient() as jest.Mocked<PrismaClient>;
    mockPrisma.payment = {
      count: jest.fn(),
      findMany: jest.fn(),
    } as any;
  });

  it('should return healthy status with statistics', async () => {
    const mockSystemHealth = {
      overall: 'healthy',
      timestamp: '2024-01-20T10:30:00.000Z',
      services: [
        {
          name: 'database',
          status: 'healthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 45,
        },
        {
          name: 'email',
          status: 'healthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 120,
        },
        {
          name: 'payment_gateway',
          status: 'healthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 200,
        },
      ],
      uptime: 86400000,
      version: '1.0.0',
    };

    mockMonitoringService.checkSystemHealth.mockResolvedValue(mockSystemHealth);

    // Mock database statistics
    mockPrisma.payment.count
      .mockResolvedValueOnce(89) // totalPayments
      .mockResolvedValueOnce(85); // completedPayments

    mockPrisma.payment.findMany.mockResolvedValue([
      { email: '<EMAIL>' },
      { email: '<EMAIL>' },
      { email: '<EMAIL>' },
    ]);

    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toEqual({
      timestamp: '2024-01-20T10:30:00.000Z',
      status: 'healthy',
      services: {
        database: 'healthy',
        email: 'healthy',
        paymentGateways: {
          flutterwave: 'healthy',
        },
      },
      statistics: {
        totalUsers: 3,
        totalSubscriptions: 85,
        activeSubscriptions: 85,
        totalPayments: 89,
        completedPayments: 85,
      },
      monitoring: {
        uptime: 86400000,
        version: '1.0.0',
        detailedServices: mockSystemHealth.services,
      },
    });
  });

  it('should return degraded status when some services are degraded', async () => {
    const mockSystemHealth = {
      overall: 'degraded',
      timestamp: '2024-01-20T10:30:00.000Z',
      services: [
        {
          name: 'database',
          status: 'healthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 45,
        },
        {
          name: 'email',
          status: 'degraded',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 6000,
          error: 'Slow response time',
        },
        {
          name: 'payment_gateway',
          status: 'healthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 200,
        },
      ],
      uptime: 86400000,
      version: '1.0.0',
    };

    mockMonitoringService.checkSystemHealth.mockResolvedValue(mockSystemHealth);

    // Mock database statistics
    mockPrisma.payment.count
      .mockResolvedValueOnce(89)
      .mockResolvedValueOnce(85);
    mockPrisma.payment.findMany.mockResolvedValue([
      { email: '<EMAIL>' },
    ]);

    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.status).toBe('degraded');
    expect(data.services.email).toBe('degraded');
  });

  it('should return unhealthy status when critical services fail', async () => {
    const mockSystemHealth = {
      overall: 'unhealthy',
      timestamp: '2024-01-20T10:30:00.000Z',
      services: [
        {
          name: 'database',
          status: 'unhealthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 0,
          error: 'Connection failed',
        },
        {
          name: 'email',
          status: 'healthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 120,
        },
        {
          name: 'payment_gateway',
          status: 'healthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 200,
        },
      ],
      uptime: 86400000,
      version: '1.0.0',
    };

    mockMonitoringService.checkSystemHealth.mockResolvedValue(mockSystemHealth);

    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.status).toBe('unhealthy');
    expect(data.services.database).toBe('unhealthy');
    expect(data.statistics).toEqual({
      totalUsers: 0,
      totalSubscriptions: 0,
      activeSubscriptions: 0,
      totalPayments: 0,
      completedPayments: 0,
    });
  });

  it('should handle missing services gracefully', async () => {
    const mockSystemHealth = {
      overall: 'healthy',
      timestamp: '2024-01-20T10:30:00.000Z',
      services: [
        {
          name: 'database',
          status: 'healthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 45,
        },
        // Missing email and payment_gateway services
      ],
      uptime: 86400000,
      version: '1.0.0',
    };

    mockMonitoringService.checkSystemHealth.mockResolvedValue(mockSystemHealth);

    // Mock database statistics
    mockPrisma.payment.count
      .mockResolvedValueOnce(89)
      .mockResolvedValueOnce(85);
    mockPrisma.payment.findMany.mockResolvedValue([]);

    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.services).toEqual({
      database: 'healthy',
      email: 'unknown',
      paymentGateways: {
        flutterwave: 'unknown',
      },
    });
  });

  it('should handle monitoring service errors', async () => {
    const error = new Error('Monitoring service failed');
    mockMonitoringService.checkSystemHealth.mockRejectedValue(error);

    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data).toEqual({
      error: 'Failed to get system status',
      details: 'Monitoring service failed',
      timestamp: expect.any(String),
    });
  });

  it('should handle database statistics errors gracefully', async () => {
    const mockSystemHealth = {
      overall: 'healthy',
      timestamp: '2024-01-20T10:30:00.000Z',
      services: [
        {
          name: 'database',
          status: 'healthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 45,
        },
      ],
      uptime: 86400000,
      version: '1.0.0',
    };

    mockMonitoringService.checkSystemHealth.mockResolvedValue(mockSystemHealth);

    // Mock database error for statistics
    mockPrisma.payment.count.mockRejectedValue(new Error('Database error'));

    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.statistics).toEqual({
      totalUsers: 0,
      totalSubscriptions: 0,
      activeSubscriptions: 0,
      totalPayments: 0,
      completedPayments: 0,
    });
  });

  it('should include monitoring data in response', async () => {
    const mockSystemHealth = {
      overall: 'healthy',
      timestamp: '2024-01-20T10:30:00.000Z',
      services: [
        {
          name: 'database',
          status: 'healthy',
          lastChecked: '2024-01-20T10:30:00.000Z',
          responseTime: 45,
          details: {
            userCount: 150,
            paymentCount: 89,
            connectionPool: 'active',
          },
        },
      ],
      uptime: 86400000,
      version: '1.0.0',
    };

    mockMonitoringService.checkSystemHealth.mockResolvedValue(mockSystemHealth);

    mockPrisma.payment.count
      .mockResolvedValueOnce(89)
      .mockResolvedValueOnce(85);
    mockPrisma.payment.findMany.mockResolvedValue([]);

    const response = await GET();
    const data = await response.json();

    expect(data.monitoring).toEqual({
      uptime: 86400000,
      version: '1.0.0',
      detailedServices: mockSystemHealth.services,
    });
  });
});
