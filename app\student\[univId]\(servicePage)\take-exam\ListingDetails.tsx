"use client"

import { useState } from "react"
import { Clock, CheckCircle2, Eye, MoreVertical, Calendar } from "lucide-react"
import Link from "next/link"

interface ExamSession {
  id: string
  examTitle: string
  startTime: string
  status: "active" | "completed"
}

// Mock data for demonstration


interface ExamSessionsTableProps {
  sessions: ExamSession[],
  univId: string

}

export default function ExamSessionsTable({ sessions ,univId }: ExamSessionsTableProps) {
  const [selectedSessions, setSelectedSessions] = useState<Set<string>>(new Set())
  const [sortBy, setSortBy] = useState<"startTime" | "examTitle" | "status">("startTime")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24 && diffInHours > -24) {
      // Show relative time for recent dates
      if (diffInHours < 1 && diffInHours > -1) {
        const diffInMinutes = Math.floor(diffInHours * 60)
        if (diffInMinutes === 0) return "Just now"
        if (diffInMinutes > 0) return `${diffInMinutes}m ago`
        return `In ${Math.abs(diffInMinutes)}m`
      }
      const hours = Math.floor(Math.abs(diffInHours))
      if (diffInHours > 0) return `${hours}h ago`
      return `In ${hours}h`
    }

    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: date.getFullYear() !== now.getFullYear() ? "numeric" : undefined,
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const handleSort = (column: "startTime" | "examTitle" | "status") => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortBy(column)
      setSortOrder("asc")
    }
  }

  const sortedSessions = [...sessions].sort((a, b) => {
    let aValue: string | number = a[sortBy]
    let bValue: string | number = b[sortBy]

    if (sortBy === "startTime") {
      aValue = new Date(a.startTime).getTime()
      bValue = new Date(b.startTime).getTime()
    }

    if (typeof aValue === "string") {
      aValue = aValue.toLowerCase()
      bValue = (bValue as string).toLowerCase()
    }

    if (sortOrder === "asc") {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
    }
  })

  const handleSelectSession = (sessionId: string) => {
    const newSelected = new Set(selectedSessions)
    if (newSelected.has(sessionId)) {
      newSelected.delete(sessionId)
    } else {
      newSelected.add(sessionId)
    }
    setSelectedSessions(newSelected)
  }

  const handleSelectAll = () => {
    if (selectedSessions.size === sessions.length) {
      setSelectedSessions(new Set())
    } else {
      setSelectedSessions(new Set(sessions.map((s) => s.id)))
    }
  }

  const getStatusBadge = (status: "active" | "completed") => {
    if (status === "active") {
      return (
        <span className="inline-flex items-center gap-2 bg-green-100 text-green-700 text-sm font-semibold px-3 py-1.5 rounded-full border border-green-200">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          Active
        </span>
      )
    } else {
      return (
        <span className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 text-sm font-semibold px-3 py-1.5 rounded-full border border-gray-200">
          <CheckCircle2 className="w-3 h-3" />
          Completed
        </span>
      )
    }
  }

  const getSortIcon = (column: string) => {
    if (sortBy !== column) {
      return (
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
          />
        </svg>
      )
    }

    if (sortOrder === "asc") {
      return (
        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
        </svg>
      )
    } else {
      return (
        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      )
    }
  }

  const activeCount = sessions.filter((s) => s.status === "active").length
  const completedCount = sessions.filter((s) => s.status === "completed").length

  return (
    <div className="bg-white p-4">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-xl font-bold text-gray-900 mb-2">Exam Sessions</h1>
            <p className="text-lg text-gray-600">Monitor and manage all exam sessions</p>
          </div>

          <div className="flex items-center gap-4">
            <div className="bg-white border border-gray-200 rounded-2xl p-4 flex items-center gap-6">
              <div className="text-center">
                <div className="text-md font-bold text-green-600">{activeCount}</div>
                <div className="text-sm text-gray-500">Active</div>
              </div>
              <div className="w-px h-8 bg-gray-200"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">{completedCount}</div>
                <div className="text-sm text-gray-500">Completed</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedSessions.size > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-2xl p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="text-sm font-semibold text-green-800">
                  {selectedSessions.size} session{selectedSessions.size !== 1 ? "s" : ""} selected
                </div>
              </div>
              <div className="flex items-center gap-3">
                <button className="text-green-700 hover:text-green-800 font-medium px-4 py-2 rounded-lg hover:bg-green-100 transition-colors text-sm">
                  Export Selected
                </button>
                <button className="text-red-600 hover:text-red-700 font-medium px-4 py-2 rounded-lg hover:bg-red-50 transition-colors text-sm">
                  Archive Selected
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Table */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50 border-b border-gray-200">
                
                <th className="text-left p-6">
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedSessions.size === sessions.length && sessions.length > 0}
                      onChange={handleSelectAll}
                      className="w-5 h-5 text-green-600 border-gray-300 rounded focus:ring-green-500"
                    />
                    <span className="text-sm font-semibold text-gray-700">Select All</span>
                  </label>
                </th>
                <th className="text-left p-6">
                  <button
                    onClick={() => handleSort("examTitle")}
                    className="flex items-center gap-2 text-sm font-semibold text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    Exam Title
                    {getSortIcon("examTitle")}
                  </button>
                </th>
                <th className="text-left p-6">
                  <button
                    onClick={() => handleSort("startTime")}
                    className="flex items-center gap-2 text-sm font-semibold text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    Start Time
                    {getSortIcon("startTime")}
                  </button>
                </th>
                <th className="text-left p-6">
                  <button
                    onClick={() => handleSort("status")}
                    className="flex items-center gap-2 text-sm font-semibold text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    Status
                    {getSortIcon("status")}
                  </button>
                </th>
                <th className="text-left p-6">
                  <span className="text-sm font-semibold text-gray-700">Actions</span>
                </th>
                
              </tr>
            </thead>
            <tbody>
              {sortedSessions.map((session, index) => (
                <tr
                  key={session.id}
                  className={`border-b border-gray-100 hover:bg-gray-50 transition-colors ${
                    selectedSessions.has(session.id) ? "bg-green-50" : ""
                  }`}
                >
                  <td className="p-6">
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedSessions.has(session.id)}
                        onChange={() => handleSelectSession(session.id)}
                        className="w-5 h-5 text-green-600 border-gray-300 rounded focus:ring-green-500"
                      />
                      <div className="w-8 h-8 bg-gray-100 border border-gray-200 rounded-xl flex items-center justify-center text-sm font-bold text-gray-600">
                        {index + 1}
                      </div>
                    </label>
                  </td>
                  <td className="p-6">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 border border-green-200 rounded-2xl flex items-center justify-center">
                        <Calendar className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900 text-lg">{session.examTitle}</div>
                        <div className="text-sm text-gray-500">ID: {session.id}</div>
                      </div>
                    </div>
                  </td>
                  <td className="p-6">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-900 font-medium">{formatDateTime(session.startTime)}</span>
                    </div>
                  </td>
                  <td className="p-6">{getStatusBadge(session.status)}</td>
                  <td className="p-6">
                    <div className="flex items-center gap-2">
                      <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                        <MoreVertical className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {sessions.length === 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Calendar className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">No exam sessions found</h3>
            <p className="text-gray-500 text-lg">Exam sessions will appear here once they are created.</p>
          </div>
        )}

        {/* Pagination Footer */}
        {sessions.length > 0 && (
          <div className="border-t border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Showing <span className="font-semibold text-gray-900">{sessions.length}</span> exam sessions
              </div>
              <div className="flex items-center gap-2">
                <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                  Previous
                </button>
                <button className="px-4 py-2 text-sm font-medium bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                  1
                </button>
                <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
