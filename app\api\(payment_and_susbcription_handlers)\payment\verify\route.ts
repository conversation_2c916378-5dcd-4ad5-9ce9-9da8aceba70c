import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { PaymentService } from '@/lib/payment-service';
import { EmailService } from '@/lib/email-service';
import prisma from '@/app/lib/prisma';
import { connect } from 'http2';
import { createToken } from '@/app/lib/token';
const paymentService = new PaymentService();

export async function POST(req: Request) {
  try {
    const { tx_ref, transaction_id, status } = await req.json();

    if (!tx_ref) {
      return NextResponse.json(
        { success: false, error: 'Transaction reference is required' },
        { status: 400 }
      );
    }

    // Handle cancellation status
    if (status === 'cancelled' || status === 'canceled') {
      // Find payment record and mark as cancelled
      const payment = await prisma.payment.findFirst({
        where: { token: tx_ref },
      });

      if (payment) {
        await prisma.payment.update({
          where: { id: payment.id },
          data: {
            status: 'cancelled',
          },
        });
      }

      return NextResponse.json({
        success: false,
        error: 'Payment was cancelled by user',
        status: 'cancelled',
      });
    }

    // Find payment record
    const payment = await prisma.payment.findFirst({
      where: { token: tx_ref },
    });

    if (!payment) {
      return NextResponse.json(
        { success: false, error: 'Payment not found' },
        { status: 404 }
      );
    }

    // Verify payment with Flutterwave
    const verification = await paymentService.verifyPayment(tx_ref);

    if (verification.success && verification.data) {
      // Check if payment was already completed to prevent duplicate processing
      if (payment.status === 'completed') {
        console.log('Payment already verified:', tx_ref);
        
        return NextResponse.json({
          success: true,
          data: verification.data,
          message: 'Payment already verified',
        });
      }

      // Update payment status
      await prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: 'completed',
        },
      });
      const admin = await prisma.admin.findFirst({where:{email:payment.email}})
      if(!admin){
        return NextResponse.json(
          { success: false, error: 'Admin not found' },
          { status: 404 }
        );
      }
      const newSub =  await prisma.subscription.create({
        data:{
          activeDate:'null',
          adminId:admin.id,
          paymentId:payment.id,
          planId:payment.planId,
        },
      })
      const token =  await createToken({adminId:admin.id,subscriptionId:newSub.id,email:admin.email})
      await prisma.tokens.create({
        data:{
          token:token
        }
      })
      // Send receipt email for newly completed payment
      const emailResult = await EmailService.sendPaymentConfirmationEmail(
        payment.email,
        token,
        payment.amount,
        verification.data.currency || 'XAF',
        'Subscription Plan' // Plan name
      );

      if (emailResult.success) {
        console.log('Receipt email sent to:', payment.email);
      } else {
        console.error('Failed to send receipt email:', emailResult.message);
        // Don't fail the verification if email fails
      }
     
      return NextResponse.json({
        success: true,
        data: {...verification.data,token},
        message: 'Payment verified successfully',
      });
    } else {
      // Update payment as failed
      await prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: 'failed',
        },
      });

      return NextResponse.json({
        success: false,
        error: verification.error || 'Payment verification failed',
      });
    }
  } catch (error: any) {
    console.error('Payment verification failed:', error);
    return NextResponse.json(
      { success: false, error: 'Payment verification failed' },
      { status: 500 }
    );
  }
}
