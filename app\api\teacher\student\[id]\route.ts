import { NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifyInstructorSession } from '@/app/lib/instructor-session';

// GET: Get specific student details
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const instructor = await verifyInstructorSession();
    if (!instructor) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const studentId = params.id;

    const student = await prisma.student.findFirst({
      where: {
        id: studentId,
        instructor: { some: { id: instructor.id } }
      },
      select: {
        id: true,
        name: true,
        email: true,
        status: true,
        averageScore: true,
        examsCompleted: true,
        lastLogin: true,
        joinDate: true,
        createdAt: true,
        updatedAt: true,
        examSessions: {
          select: {
            id: true,
            score: true,
            status: true,
            startedAt: true,
            endedAt: true,
            exam: {
              select: {
                id: true,
                title: true,
                createdAt: true
              }
            }
          },
          orderBy: { startedAt: 'desc' }
        }
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      );
    }

    // Calculate detailed analytics
    const completedExams = student.examSessions.filter(session => session.status === 'completed');
    const scores = completedExams.map(session => session.score || 0);
    
    const analytics = {
      totalExams: student.examSessions.length,
      completedExams: completedExams.length,
      averageScore: scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0,
      highestScore: scores.length > 0 ? Math.max(...scores) : 0,
      lowestScore: scores.length > 0 ? Math.min(...scores) : 0,
      completionRate: student.examSessions.length > 0 ? (completedExams.length / student.examSessions.length) * 100 : 0,
      recentActivity: student.examSessions.slice(0, 10)
    };

    // Calculate performance trend
    const recentScores = scores.slice(0, 5);
    let performanceTrend = 'stable';
    if (recentScores.length >= 3) {
      const firstHalf = recentScores.slice(0, Math.ceil(recentScores.length / 2));
      const secondHalf = recentScores.slice(Math.ceil(recentScores.length / 2));
      const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
      
      if (secondAvg > firstAvg + 5) performanceTrend = 'improving';
      else if (secondAvg < firstAvg - 5) performanceTrend = 'declining';
    }

    return NextResponse.json({
      ...student,
      analytics,
      performanceTrend
    });

  } catch (error) {
    console.error('Error fetching student:', error);
    return NextResponse.json(
      { error: 'Failed to fetch student details' },
      { status: 500 }
    );
  }
}

// PUT: Update student information
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const instructor = await verifyInstructorSession();
    if (!instructor) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const studentId = params.id;
    const body = await request.json();
    const { name, email, status } = body;

    // Verify student belongs to instructor
    const existingStudent = await prisma.student.findFirst({
      where: {
        id: studentId,
        instructor: { some: { id: instructor.id } }
      }
    });

    if (!existingStudent) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      );
    }

    // Update student
    const updatedStudent = await prisma.student.update({
      where: { id: studentId },
      data: {
        ...(name && { name }),
        ...(email && { email }),
        ...(status && { status }),
        updatedAt: new Date()
      },
      select: {
        id: true,
        name: true,
        email: true,
        status: true,
        averageScore: true,
        examsCompleted: true,
        updatedAt: true
      }
    });

    return NextResponse.json(updatedStudent);

  } catch (error) {
    console.error('Error updating student:', error);
    return NextResponse.json(
      { error: 'Failed to update student' },
      { status: 500 }
    );
  }
}

// DELETE: Remove student
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const instructor = await verifyInstructorSession();
    if (!instructor) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const studentId = params.id;

    // Verify student belongs to instructor
    const existingStudent = await prisma.student.findFirst({
      where: {
        id: studentId,
        instructor: { some: { id: instructor.id } }
      }
    });

    if (!existingStudent) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      );
    }

    // Instead of hard delete, we'll soft delete by changing status
    await prisma.student.update({
      where: { id: studentId },
      data: {
        status: 'inactive',
        updatedAt: new Date()
      }
    });

    return NextResponse.json(
      { message: 'Student deactivated successfully' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error deleting student:', error);
    return NextResponse.json(
      { error: 'Failed to delete student' },
      { status: 500 }
    );
  }
}
