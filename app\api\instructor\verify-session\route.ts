import { NextRequest, NextResponse } from 'next/server';
import { verifyInstructorSession } from '@/app/lib/instructor-session';
import { verifyInstructorTokenSession } from '@/app/lib/instructor-token-session';

export async function GET(req: NextRequest) {
  const user = await verifyInstructorTokenSession();
  if (!user) {
    return NextResponse.json({ success: false });
  }
  return NextResponse.json({ success: true, user });
}
