import React from 'react'
import Form from './Form'
import LoginPage from './LeftForm'
import prisma from '@/app/lib/prisma'
import { verifyInstructorSession } from '@/app/lib/instructor-session'
import { redirect } from 'next/navigation'

const page = async ({params}:{params:Promise<{univId:string}>}) => {
    const {univId} = await params
    const session = await verifyInstructorSession()
    if(session){
      redirect(`/teacher/${univId}/dashboard`)
    }
    const universityData = await prisma.univ.findUnique({where:{id:univId},select:{name:true}})
    
  return (
    <LoginPage univID={univId} univName={universityData?.name || "Ict university"} />
  )
}

export default page
