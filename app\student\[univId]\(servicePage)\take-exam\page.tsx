import { verifyStudentSession } from "@/app/lib/student-session";
import { redirect } from "next/navigation";
import ExamSessionsPage from "./ListingDetails";

const page = async ({params}:{params:Promise<{univId:string}>}) => {
    const { univId } = await params;
    const session = await verifyStudentSession();
    if (!session) {
        // redirect to login page if not logged in
        redirect('/student/' + univId + '/login');
    }
    const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/exam/${univId}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    });
    const data = await res.json();
    if (!res.ok) {
        console.error('Failed to fetch exam sessions:', data.error);
        return <div>Error loading exam sessions</div>;
    }
  return (
    <div className="max-w-[calc(100vw-16rem)] w-full ">
      <ExamSessionsPage sessions={data} univId={univId} />
    </div>
  )
}

export default page;