"use client"

import { useState, useEffect } from "react"
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Database, 
  Mail, 
  CreditCard, 
  Globe,
  RefreshCw,
  TrendingUp,
  Server
} from "lucide-react"

interface ServiceStatus {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded' | 'not_configured';
  lastChecked: string;
  responseTime?: number;
  error?: string;
  details?: any;
}

interface SystemHealth {
  overall: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  services: ServiceStatus[];
  uptime: number;
  version: string;
}

interface MonitoringData {
  health: SystemHealth;
  metrics: {
    uptime: number;
    serviceFailures: Record<string, number>;
    alertConfig: {
      enabled: boolean;
      recipientCount: number;
      thresholds: {
        responseTime: number;
        errorRate: number;
        consecutiveFailures: number;
      };
    };
  };
}

export default function MonitoringDashboard() {
  const [monitoringData, setMonitoringData] = useState<MonitoringData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  const fetchMonitoringData = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch('/api/monitoring')
      if (!response.ok) {
        throw new Error('Failed to fetch monitoring data')
      }
      const data = await response.json()
      setMonitoringData(data)
      setLastRefresh(new Date())
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMonitoringData()
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchMonitoringData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'degraded':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'unhealthy':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'not_configured':
        return <Clock className="h-5 w-5 text-gray-400" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'unhealthy':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'not_configured':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getServiceIcon = (serviceName: string) => {
    switch (serviceName) {
      case 'database':
        return <Database className="h-6 w-6" />
      case 'email':
        return <Mail className="h-6 w-6" />
      case 'payment_gateway':
        return <CreditCard className="h-6 w-6" />
      case 'external_dependencies':
        return <Globe className="h-6 w-6" />
      default:
        return <Server className="h-6 w-6" />
    }
  }

  const formatUptime = (uptime: number) => {
    const seconds = Math.floor(uptime / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`
    if (hours > 0) return `${hours}h ${minutes % 60}m`
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`
    return `${seconds}s`
  }

  if (loading && !monitoringData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading monitoring data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchMonitoringData}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">System Monitoring</h1>
              <p className="text-gray-600 mt-2">Real-time system health and performance metrics</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </div>
              <button
                onClick={fetchMonitoringData}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>

        {monitoringData && (
          <>
            {/* Overall Status */}
            <div className="mb-8">
              <div className={`rounded-lg border-2 p-6 ${getStatusColor(monitoringData.health.overall)}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(monitoringData.health.overall)}
                    <div>
                      <h2 className="text-xl font-semibold">
                        System Status: {monitoringData.health.overall.toUpperCase()}
                      </h2>
                      <p className="text-sm opacity-75">
                        Version {monitoringData.health.version} • Uptime: {formatUptime(monitoringData.health.uptime)}
                      </p>
                    </div>
                  </div>
                  <Activity className="h-8 w-8" />
                </div>
              </div>
            </div>

            {/* Services Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {monitoringData.health.services.map((service) => (
                <div key={service.name} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getServiceIcon(service.name)}
                      <h3 className="font-semibold text-gray-900 capitalize">
                        {service.name.replace('_', ' ')}
                      </h3>
                    </div>
                    {getStatusIcon(service.status)}
                  </div>
                  
                  <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                    {service.status.toUpperCase()}
                  </div>
                  
                  {service.responseTime && (
                    <div className="mt-3 text-sm text-gray-600">
                      Response: {service.responseTime}ms
                    </div>
                  )}
                  
                  {service.error && (
                    <div className="mt-3 text-sm text-red-600 truncate" title={service.error}>
                      Error: {service.error}
                    </div>
                  )}
                  
                  <div className="mt-3 text-xs text-gray-500">
                    Last checked: {new Date(service.lastChecked).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>

            {/* Metrics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Alert Configuration */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Alert Configuration</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Alerts Enabled:</span>
                    <span className={`font-medium ${monitoringData.metrics.alertConfig.enabled ? 'text-green-600' : 'text-red-600'}`}>
                      {monitoringData.metrics.alertConfig.enabled ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Recipients:</span>
                    <span className="font-medium">{monitoringData.metrics.alertConfig.recipientCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Response Time Threshold:</span>
                    <span className="font-medium">{monitoringData.metrics.alertConfig.thresholds.responseTime}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Failure Threshold:</span>
                    <span className="font-medium">{monitoringData.metrics.alertConfig.thresholds.consecutiveFailures}</span>
                  </div>
                </div>
              </div>

              {/* Service Failures */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Failures</h3>
                <div className="space-y-3">
                  {Object.entries(monitoringData.metrics.serviceFailures).map(([service, failures]) => (
                    <div key={service} className="flex justify-between">
                      <span className="text-gray-600 capitalize">{service.replace('_', ' ')}:</span>
                      <span className={`font-medium ${failures > 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {failures} failures
                      </span>
                    </div>
                  ))}
                  {Object.keys(monitoringData.metrics.serviceFailures).length === 0 && (
                    <p className="text-gray-500 text-center py-4">No service failures recorded</p>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
