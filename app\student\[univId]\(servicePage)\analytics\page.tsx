"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>pen,
  Filter,
  Download,
  Share2,
  Bell,
  HelpCircle,
  ChevronDown,
  TrendingUp,
  Users,
  Clock,
  Target,
  BarChart3,
  MessageSquare,
} from "lucide-react"

export default function AnalyticsPage() {
  const [dateRange, setDateRange] = useState("last-30-days")
  const [courseFilter, setCourseFilter] = useState("all")
  const [questionTypeFilter, setQuestionTypeFilter] = useState("all")

  // Mock data for analytics
  const examTrends = [
    { month: "Aug", published: 15, avgScore: 78.5 },
    { month: "Sep", published: 22, avgScore: 82.1 },
    { month: "Oct", published: 18, avgScore: 79.8 },
    { month: "Nov", published: 25, avgScore: 84.2 },
    { month: "Dec", published: 20, avgScore: 81.7 },
    { month: "Jan", published: 28, avgScore: 85.3 },
  ]

  const completionRates = [
    { type: "Multiple Choice", completed: 95, abandoned: 5 },
    { type: "Essay", completed: 87, abandoned: 13 },
    { type: "True/False", completed: 98, abandoned: 2 },
    { type: "Fill in Blank", completed: 89, abandoned: 11 },
  ]

  const questionPerformance = [
    { id: "Q001", text: "What is the derivative of x²?", difficulty: "Easy", passRate: 92, avgTime: 45 },
    { id: "Q002", text: "Explain the concept of limits", difficulty: "Hard", passRate: 68, avgTime: 180 },
    { id: "Q003", text: "Solve for x: 2x + 5 = 15", difficulty: "Medium", passRate: 85, avgTime: 75 },
    { id: "Q004", text: "True or False: π > 3.14", difficulty: "Easy", passRate: 78, avgTime: 25 },
    { id: "Q005", text: "Calculate the integral of 2x", difficulty: "Medium", passRate: 71, avgTime: 95 },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-600 text-white">
                  <BookOpen className="h-4 w-4" />
                </div>
                <span className="text-xl font-bold text-blue-600">ExamPro</span>
              </div>
              <div className="hidden md:block h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
            </div>
            <div className="flex items-center gap-4">
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <Bell className="h-5 w-5" />
              </button>
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <HelpCircle className="h-5 w-5" />
              </button>
              <div className="flex gap-2">
                <button className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors font-medium">
                  <Share2 className="h-4 w-4" />
                  Share
                </button>
                <button className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                  <Download className="h-4 w-4" />
                  Export Data
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* Filters */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex gap-3">
                {/* Date Range Filter */}
                <div className="relative">
                  <select
                    value={dateRange}
                    onChange={(e) => setDateRange(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="last-7-days">Last 7 Days</option>
                    <option value="last-30-days">Last 30 Days</option>
                    <option value="last-90-days">Last 90 Days</option>
                    <option value="last-6-months">Last 6 Months</option>
                    <option value="last-year">Last Year</option>
                    <option value="custom">Custom Range</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Course Filter */}
                <div className="relative">
                  <select
                    value={courseFilter}
                    onChange={(e) => setCourseFilter(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Courses</option>
                    <option value="mathematics">Mathematics</option>
                    <option value="physics">Physics</option>
                    <option value="chemistry">Chemistry</option>
                    <option value="biology">Biology</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Question Type Filter */}
                <div className="relative">
                  <select
                    value={questionTypeFilter}
                    onChange={(e) => setQuestionTypeFilter(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Question Types</option>
                    <option value="multiple-choice">Multiple Choice</option>
                    <option value="essay">Essay</option>
                    <option value="true-false">True/False</option>
                    <option value="fill-blank">Fill in the Blank</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                <button className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
                  <Filter className="h-4 w-4" />
                  Advanced Filters
                </button>
              </div>
            </div>
          </div>

          {/* High-Level Metrics */}
          <div className="grid gap-6 md:grid-cols-4 mb-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Exams Published</p>
                  <p className="text-2xl font-bold text-gray-900">128</p>
                  <div className="flex items-center gap-1 text-xs font-medium text-green-600">
                    <TrendingUp className="h-3 w-3" />
                    +15.2% from last month
                  </div>
                </div>
                <div className="rounded-full bg-blue-100 p-3">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Score</p>
                  <p className="text-2xl font-bold text-gray-900">82.3%</p>
                  <div className="flex items-center gap-1 text-xs font-medium text-green-600">
                    <TrendingUp className="h-3 w-3" />
                    +3.1% improvement
                  </div>
                </div>
                <div className="rounded-full bg-green-100 p-3">
                  <Target className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Students</p>
                  <p className="text-2xl font-bold text-gray-900">1,245</p>
                  <div className="flex items-center gap-1 text-xs font-medium text-green-600">
                    <TrendingUp className="h-3 w-3" />
                    +8.7% growth
                  </div>
                </div>
                <div className="rounded-full bg-purple-100 p-3">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg. Completion Time</p>
                  <p className="text-2xl font-bold text-gray-900">47 min</p>
                  <div className="flex items-center gap-1 text-xs font-medium text-red-600">
                    <TrendingUp className="h-3 w-3 rotate-180" />
                    -5.2% faster
                  </div>
                </div>
                <div className="rounded-full bg-yellow-100 p-3">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Charts Row */}
          <div className="grid gap-6 md:grid-cols-2 mb-6">
            {/* Exam Publication Trends */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Exam Publication Trends</h3>
                  <p className="text-sm text-gray-600">Monthly exam creation and average scores</p>
                </div>
                <button className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-700">
                  <MessageSquare className="h-4 w-4" />
                  Add Note
                </button>
              </div>
              <ExamTrendsChart data={examTrends} />
            </div>

            {/* Completion vs Abandonment */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Completion Rates by Question Type</h3>
                  <p className="text-sm text-gray-600">Student completion vs abandonment rates</p>
                </div>
                <button className="text-sm text-blue-600 hover:text-blue-700">View Details</button>
              </div>
              <CompletionChart data={completionRates} />
            </div>
          </div>

          {/* Question Performance Heatmap */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Question Performance Heatmap</h3>
                <p className="text-sm text-gray-600">Click on any question to drill down into response distribution</p>
              </div>
              <div className="flex gap-2">
                <button className="text-sm text-blue-600 hover:text-blue-700">Export Data</button>
                <button className="text-sm text-blue-600 hover:text-blue-700">Share View</button>
              </div>
            </div>
            <QuestionHeatmap data={questionPerformance} />
          </div>

          {/* Cohort Comparison */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Cohort Performance Comparison</h3>
                <p className="text-sm text-gray-600">Compare different student groups and demographics</p>
              </div>
              <button className="inline-flex items-center gap-2 bg-blue-600 text-white px-3 py-1.5 rounded-md hover:bg-blue-700 transition-colors text-sm">
                <Filter className="h-4 w-4" />
                Configure Cohorts
              </button>
            </div>
            <CohortComparison />
          </div>
        </div>
      </main>
    </div>
  )
}

function ExamTrendsChart({ data }: { data: any[] }) {
  return (
    <div className="h-64 w-full">
      {/* This would be replaced with a real chart library */}
      <div className="relative h-full border rounded-md bg-gray-50 flex items-end justify-around p-4">
        {data.map((item, index) => (
          <div key={index} className="flex flex-col items-center">
            <div className="flex flex-col items-center mb-2">
              <div className="w-8 bg-blue-500 rounded-t" style={{ height: `${(item.published / 30) * 120}px` }}></div>
              <div className="text-xs text-gray-600 mt-1">{item.published}</div>
            </div>
            <div className="text-xs font-medium text-gray-700">{item.month}</div>
          </div>
        ))}
      </div>
      <div className="flex items-center justify-center gap-4 mt-4">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-blue-500 rounded"></div>
          <span className="text-sm text-gray-600">Exams Published</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-500 rounded"></div>
          <span className="text-sm text-gray-600">Average Score</span>
        </div>
      </div>
    </div>
  )
}

function CompletionChart({ data }: { data: any[] }) {
  return (
    <div className="space-y-4">
      {data.map((item, index) => (
        <div key={index} className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="font-medium">{item.type}</span>
            <span className="text-gray-600">{item.completed}% completion</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-green-500 h-2 rounded-full" style={{ width: `${item.completed}%` }}></div>
          </div>
        </div>
      ))}
    </div>
  )
}

function QuestionHeatmap({ data }: { data: any[] }) {
  const getHeatmapColor = (passRate: number) => {
    if (passRate >= 80) return "bg-green-500"
    if (passRate >= 60) return "bg-yellow-500"
    return "bg-red-500"
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left py-2 px-4 text-sm font-medium text-gray-600">Question ID</th>
            <th className="text-left py-2 px-4 text-sm font-medium text-gray-600">Question Text</th>
            <th className="text-left py-2 px-4 text-sm font-medium text-gray-600">Difficulty</th>
            <th className="text-left py-2 px-4 text-sm font-medium text-gray-600">Pass Rate</th>
            <th className="text-left py-2 px-4 text-sm font-medium text-gray-600">Avg Time</th>
            <th className="text-left py-2 px-4 text-sm font-medium text-gray-600">Performance</th>
          </tr>
        </thead>
        <tbody>
          {data.map((question, index) => (
            <tr key={index} className="border-b hover:bg-gray-50 cursor-pointer">
              <td className="py-3 px-4 text-sm font-medium">{question.id}</td>
              <td className="py-3 px-4 text-sm">{question.text}</td>
              <td className="py-3 px-4">
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    question.difficulty === "Easy"
                      ? "bg-green-100 text-green-800"
                      : question.difficulty === "Medium"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-red-100 text-red-800"
                  }`}
                >
                  {question.difficulty}
                </span>
              </td>
              <td className="py-3 px-4 text-sm">{question.passRate}%</td>
              <td className="py-3 px-4 text-sm">{question.avgTime}s</td>
              <td className="py-3 px-4">
                <div className="flex items-center">
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${getHeatmapColor(question.passRate)}`}
                      style={{ width: `${question.passRate}%` }}
                    ></div>
                  </div>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function CohortComparison() {
  const cohorts = [
    { name: "Fall 2024 Freshmen", avgScore: 78.5, count: 245 },
    { name: "Fall 2024 Sophomores", avgScore: 82.1, count: 198 },
    { name: "Spring 2024 Freshmen", avgScore: 75.8, count: 267 },
    { name: "Spring 2024 Sophomores", avgScore: 84.3, count: 189 },
  ]

  return (
    <div className="space-y-4">
      {cohorts.map((cohort, index) => (
        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
          <div>
            <div className="font-medium text-gray-900">{cohort.name}</div>
            <div className="text-sm text-gray-600">{cohort.count} students</div>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-lg font-bold text-gray-900">{cohort.avgScore}%</div>
              <div className="text-sm text-gray-600">Average Score</div>
            </div>
            <div className="w-32 bg-gray-200 rounded-full h-2">
              <div className="bg-blue-500 h-2 rounded-full" style={{ width: `${cohort.avgScore}%` }}></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
