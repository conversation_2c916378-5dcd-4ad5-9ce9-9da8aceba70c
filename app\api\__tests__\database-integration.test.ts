import { GET as getStudentExams } from '../student/[univID]/exams/route';
import { GET as getAdminStudents } from '../admin/students/route';
import { GET as getQuestions } from '../questions/route';
import { PrismaClient } from '@prisma/client';
import { NextRequest } from 'next/server';

// Mock Prisma Client
jest.mock('@prisma/client');

describe('Database Integration Tests', () => {
  let mockPrisma: jest.Mocked<PrismaClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockPrisma = new PrismaClient() as jest.Mocked<PrismaClient>;
    
    // Mock Prisma methods
    mockPrisma.exam = {
      findMany: jest.fn(),
    } as any;
    
    mockPrisma.student = {
      findMany: jest.fn(),
    } as any;
    
    mockPrisma.question = {
      findMany: jest.fn(),
    } as any;
    
    mockPrisma.examResult = {
      findMany: jest.fn(),
    } as any;
  });

  describe('Student Exams API', () => {
    it('should fetch student exams successfully', async () => {
      const mockExams = [
        {
          id: 'exam-1',
          title: 'Mathematics Final',
          description: 'Final exam for Mathematics course',
          duration: 120,
          totalQuestions: 50,
          passingScore: 70,
          isActive: true,
          createdAt: new Date('2024-01-15T10:00:00Z'),
          updatedAt: new Date('2024-01-15T10:00:00Z'),
        },
        {
          id: 'exam-2',
          title: 'Physics Midterm',
          description: 'Midterm exam for Physics course',
          duration: 90,
          totalQuestions: 30,
          passingScore: 65,
          isActive: true,
          createdAt: new Date('2024-01-10T14:00:00Z'),
          updatedAt: new Date('2024-01-10T14:00:00Z'),
        },
      ];

      mockPrisma.exam.findMany.mockResolvedValue(mockExams);

      const request = new NextRequest('http://localhost:3000/api/student/univ-123/exams');
      const response = await getStudentExams(request, { params: { univID: 'univ-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.exams).toHaveLength(2);
      expect(data.exams[0]).toEqual({
        id: 'exam-1',
        title: 'Mathematics Final',
        description: 'Final exam for Mathematics course',
        duration: 120,
        totalQuestions: 50,
        passingScore: 70,
        isActive: true,
        createdAt: '2024-01-15T10:00:00.000Z',
        updatedAt: '2024-01-15T10:00:00.000Z',
      });

      expect(mockPrisma.exam.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });

    it('should handle database errors gracefully', async () => {
      const error = new Error('Database connection failed');
      mockPrisma.exam.findMany.mockRejectedValue(error);

      const request = new NextRequest('http://localhost:3000/api/student/univ-123/exams');
      const response = await getStudentExams(request, { params: { univID: 'univ-123' } });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toEqual({
        error: 'Failed to fetch exams',
        details: 'Database connection failed',
      });
    });

    it('should return empty array when no exams found', async () => {
      mockPrisma.exam.findMany.mockResolvedValue([]);

      const request = new NextRequest('http://localhost:3000/api/student/univ-123/exams');
      const response = await getStudentExams(request, { params: { univID: 'univ-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.exams).toEqual([]);
      expect(data.total).toBe(0);
    });
  });

  describe('Admin Students API', () => {
    it('should fetch students successfully', async () => {
      const mockStudents = [
        {
          id: 'student-1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          studentId: 'STU001',
          course: 'Computer Science',
          year: 3,
          isActive: true,
          createdAt: new Date('2024-01-01T00:00:00Z'),
          updatedAt: new Date('2024-01-15T10:00:00Z'),
        },
        {
          id: 'student-2',
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          studentId: 'STU002',
          course: 'Mathematics',
          year: 2,
          isActive: true,
          createdAt: new Date('2024-01-02T00:00:00Z'),
          updatedAt: new Date('2024-01-16T10:00:00Z'),
        },
      ];

      mockPrisma.student.findMany.mockResolvedValue(mockStudents);

      const request = new NextRequest('http://localhost:3000/api/admin/students');
      const response = await getAdminStudents(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.students).toHaveLength(2);
      expect(data.students[0]).toEqual({
        id: 'student-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        studentId: 'STU001',
        course: 'Computer Science',
        year: 3,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-15T10:00:00.000Z',
      });

      expect(mockPrisma.student.findMany).toHaveBeenCalledWith({
        orderBy: {
          createdAt: 'desc',
        },
      });
    });

    it('should handle student filtering by course', async () => {
      const mockStudents = [
        {
          id: 'student-1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          studentId: 'STU001',
          course: 'Computer Science',
          year: 3,
          isActive: true,
          createdAt: new Date('2024-01-01T00:00:00Z'),
          updatedAt: new Date('2024-01-15T10:00:00Z'),
        },
      ];

      mockPrisma.student.findMany.mockResolvedValue(mockStudents);

      const request = new NextRequest('http://localhost:3000/api/admin/students?course=Computer%20Science');
      const response = await getAdminStudents(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.students).toHaveLength(1);
      expect(data.students[0].course).toBe('Computer Science');
    });

    it('should handle database errors in student fetching', async () => {
      const error = new Error('Student query failed');
      mockPrisma.student.findMany.mockRejectedValue(error);

      const request = new NextRequest('http://localhost:3000/api/admin/students');
      const response = await getAdminStudents(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toEqual({
        error: 'Failed to fetch students',
        details: 'Student query failed',
      });
    });
  });

  describe('Questions API', () => {
    it('should fetch questions successfully', async () => {
      const mockQuestions = [
        {
          id: 'question-1',
          text: 'What is the capital of France?',
          type: 'multiple_choice',
          options: ['London', 'Berlin', 'Paris', 'Madrid'],
          correctAnswer: 'Paris',
          points: 5,
          difficulty: 'easy',
          subject: 'Geography',
          isActive: true,
          createdAt: new Date('2024-01-10T00:00:00Z'),
          updatedAt: new Date('2024-01-10T00:00:00Z'),
        },
        {
          id: 'question-2',
          text: 'Solve: 2x + 5 = 15',
          type: 'short_answer',
          options: [],
          correctAnswer: 'x = 5',
          points: 10,
          difficulty: 'medium',
          subject: 'Mathematics',
          isActive: true,
          createdAt: new Date('2024-01-11T00:00:00Z'),
          updatedAt: new Date('2024-01-11T00:00:00Z'),
        },
      ];

      mockPrisma.question.findMany.mockResolvedValue(mockQuestions);

      const request = new NextRequest('http://localhost:3000/api/questions');
      const response = await getQuestions(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.questions).toHaveLength(2);
      expect(data.questions[0]).toEqual({
        id: 'question-1',
        text: 'What is the capital of France?',
        type: 'multiple_choice',
        options: ['London', 'Berlin', 'Paris', 'Madrid'],
        correctAnswer: 'Paris',
        points: 5,
        difficulty: 'easy',
        subject: 'Geography',
        isActive: true,
        createdAt: '2024-01-10T00:00:00.000Z',
        updatedAt: '2024-01-10T00:00:00.000Z',
      });

      expect(mockPrisma.question.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });

    it('should filter questions by subject', async () => {
      const mockQuestions = [
        {
          id: 'question-1',
          text: 'Solve: 2x + 5 = 15',
          type: 'short_answer',
          options: [],
          correctAnswer: 'x = 5',
          points: 10,
          difficulty: 'medium',
          subject: 'Mathematics',
          isActive: true,
          createdAt: new Date('2024-01-11T00:00:00Z'),
          updatedAt: new Date('2024-01-11T00:00:00Z'),
        },
      ];

      mockPrisma.question.findMany.mockResolvedValue(mockQuestions);

      const request = new NextRequest('http://localhost:3000/api/questions?subject=Mathematics');
      const response = await getQuestions(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.questions).toHaveLength(1);
      expect(data.questions[0].subject).toBe('Mathematics');

      expect(mockPrisma.question.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          subject: 'Mathematics',
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });

    it('should filter questions by difficulty', async () => {
      const mockQuestions = [
        {
          id: 'question-1',
          text: 'What is the capital of France?',
          type: 'multiple_choice',
          options: ['London', 'Berlin', 'Paris', 'Madrid'],
          correctAnswer: 'Paris',
          points: 5,
          difficulty: 'easy',
          subject: 'Geography',
          isActive: true,
          createdAt: new Date('2024-01-10T00:00:00Z'),
          updatedAt: new Date('2024-01-10T00:00:00Z'),
        },
      ];

      mockPrisma.question.findMany.mockResolvedValue(mockQuestions);

      const request = new NextRequest('http://localhost:3000/api/questions?difficulty=easy');
      const response = await getQuestions(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.questions).toHaveLength(1);
      expect(data.questions[0].difficulty).toBe('easy');

      expect(mockPrisma.question.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          difficulty: 'easy',
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });

    it('should handle questions database errors', async () => {
      const error = new Error('Questions query failed');
      mockPrisma.question.findMany.mockRejectedValue(error);

      const request = new NextRequest('http://localhost:3000/api/questions');
      const response = await getQuestions(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toEqual({
        error: 'Failed to fetch questions',
        details: 'Questions query failed',
      });
    });
  });

  describe('Data Consistency', () => {
    it('should maintain consistent data types across APIs', async () => {
      // Test that all APIs return consistent date formats and data types
      const mockExam = {
        id: 'exam-1',
        title: 'Test Exam',
        description: 'Test Description',
        duration: 120,
        totalQuestions: 50,
        passingScore: 70,
        isActive: true,
        createdAt: new Date('2024-01-15T10:00:00Z'),
        updatedAt: new Date('2024-01-15T10:00:00Z'),
      };

      mockPrisma.exam.findMany.mockResolvedValue([mockExam]);

      const request = new NextRequest('http://localhost:3000/api/student/univ-123/exams');
      const response = await getStudentExams(request, { params: { univID: 'univ-123' } });
      const data = await response.json();

      expect(data.exams[0].createdAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(data.exams[0].updatedAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(typeof data.exams[0].duration).toBe('number');
      expect(typeof data.exams[0].totalQuestions).toBe('number');
      expect(typeof data.exams[0].passingScore).toBe('number');
      expect(typeof data.exams[0].isActive).toBe('boolean');
    });
  });
});
