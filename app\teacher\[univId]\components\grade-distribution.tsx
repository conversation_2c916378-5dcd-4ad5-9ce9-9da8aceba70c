"use client"

import { useEffect, useRef } from "react"

export function GradeDistribution() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    // Grade distribution data
    const grades = [
      { grade: "A", count: 45, color: "#10b981" },
      { grade: "B", count: 78, color: "#3b82f6" },
      { grade: "C", count: 92, color: "#f59e0b" },
      { grade: "D", count: 34, color: "#ef4444" },
      { grade: "F", count: 12, color: "#6b7280" },
    ]

    const total = grades.reduce((sum, grade) => sum + grade.count, 0)

    // Draw pie chart
    const centerX = canvas.width / 2
    const centerY = canvas.height / 2 - 20
    const radius = Math.min(centerX, centerY) * 0.7

    let startAngle = -0.5 * Math.PI

    grades.forEach((grade) => {
      const sliceAngle = (grade.count / total) * 2 * Math.PI

      ctx.beginPath()
      ctx.moveTo(centerX, centerY)
      ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle)
      ctx.closePath()
      ctx.fillStyle = grade.color
      ctx.fill()

      startAngle += sliceAngle
    })

    // Draw legend
    const legendY = canvas.height - 40
    let legendX = 20

    grades.forEach((grade) => {
      ctx.fillStyle = grade.color
      ctx.fillRect(legendX, legendY, 12, 12)

      ctx.fillStyle = "#374151"
      ctx.font = "12px Inter, sans-serif"
      ctx.textAlign = "left"
      ctx.fillText(`${grade.grade} (${grade.count})`, legendX + 18, legendY + 10)

      legendX += 70
    })
  }, [])

  return (
    <div className="h-[200px] w-full">
      <canvas ref={canvasRef} className="h-full w-full"></canvas>
    </div>
  )
}
