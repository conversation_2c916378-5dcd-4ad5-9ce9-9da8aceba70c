import type { Metada<PERSON> } from 'next'
import SideBar from './components/SideBar'
import { verifyInstructorSession } from '@/app/lib/instructor-session'
import { redirect } from 'next/navigation'

export const metadata: Metadata = {
  title: 'Instructor',
  description: 'Created with v0',
  generator: 'v0.dev',
}

export default async function RootLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode,
  params:Promise<{univId:string}>
}>) {
   const {univId} = await params
  const instructor = await verifyInstructorSession()
  console.log(instructor)
  if(!instructor) redirect(`/teacher/${univId}/login`)
  return (
    <html lang="en">
      <body>
        <div className="flex min-h-screen bg-gray-50">
      
            <SideBar univId={univId}/>
            {children}
        </div>
        
        </body>
    </html>
  )
}
