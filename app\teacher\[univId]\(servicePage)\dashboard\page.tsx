import {
  ArrowDown,
  ArrowUp,
  Bell,
  Calendar,
  ChevronDown,
  Download,
  Filter,
  HelpCircle,
  MoreHorizontal,
  Search,
  
  Users,
  
  BookOpen,
  ClipboardList,
  Award,
  
  CheckCircle,
} from "lucide-react"
import Link from "next/link"
import { Exam<PERSON>hart } from "../../components/exam-chart"
import { GradeDistribution } from "../../components/grade-distribution"
import { RecentExamsTable } from "../../components/recent-exams-table"
import SideBar from "../components/SideBar"
import { Metadata } from "next"
import { cookies } from "next/headers";

export const metadata:Metadata = {
  title: 'Instructor-Dashboard',
}
export default async function Home() {
  // Fetch instructor dashboard stats from API on the server
  let stats: {
    totalExams: number;
    activeStudents: number;
    completionRate: number;
    averageScore: number;
  } | null = null;
  let error = null;
  try {
    // Use absolute URL for API route (works in server components)
    const cookieHeader = cookies().toString();
    const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"}/api/instructor/dashboard-stats`, {
      headers: { Cookie: cookieHeader },
      cache: "no-store",
    });
    const data = await res.json();
    if (data.success) {
      stats = data.stats;
    } else {
      error = data.error || "Failed to load stats.";
    }
  } catch (e) {
    error = "Failed to load stats.";
  }

  // Loading state (should never show in server component, but fallback for suspense)
  if (!stats && !error) {
    return (
      <div className="flex-1 flex items-center justify-center min-h-[300px]">
        <span className="text-gray-500 text-lg">Loading dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center min-h-[300px]">
        <span className="text-red-500 text-lg">{error}</span>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-auto">
      <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-white px-6">
        <div className="flex flex-1 items-center gap-4">
          <div className="relative w-full max-w-md">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <input
              type="search"
              placeholder="Search exams, students..."
              className="w-full rounded-md border border-gray-300 bg-white pl-8 pr-4 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
          <div className="ml-auto flex items-center gap-4">
            <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
              <Bell className="h-5 w-5" />
              <span className="sr-only">Notifications</span>
            </button>
            <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
              <HelpCircle className="h-5 w-5" />
              <span className="sr-only">Help</span>
            </button>
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                <span className="text-sm font-medium text-blue-600">SW</span>
              </div>
              <div>
                <p className="text-sm font-medium">Dr. Sarah Wilson</p>
                <p className="text-xs text-gray-500">Instructor</p>
              </div>
            </div>
          </div>
        </div>
      </header>
      <main className="p-6">
        <div className="mb-6 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Instructor Dashboard</h1>
            <p className="text-gray-600">Welcome back! Here's your exam overview.</p>
          </div>
          <div className="flex items-center gap-2">
            <button className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-xs font-medium text-gray-700 hover:bg-gray-50">
              <Calendar className="h-3.5 w-3.5" />
              Last 30 Days
              <ChevronDown className="h-3.5 w-3.5" />
            </button>
            <button className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-xs font-medium text-gray-700 hover:bg-gray-50">
              <Filter className="h-3.5 w-3.5" />
              Filter
            </button>
            <button className="flex items-center gap-1 rounded-md bg-blue-600 px-3 py-2 text-xs font-medium text-white hover:bg-blue-700">
              <Download className="h-3.5 w-3.5" />
              Export Report
            </button>
          </div>
        </div>

        {/* Stats Row */}
        <div className="mb-6 grid gap-6 md:grid-cols-4">
          {/* Total Exams */}
          <div className="rounded-lg border border-gray-200 bg-white p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Exams</p>
                <p className="text-2xl font-bold text-gray-900">{stats!.totalExams}</p>
                {/* You can add a trend indicator here if you have one */}
              </div>
              <div className="rounded-full bg-blue-100 p-3">
                <ClipboardList className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          {/* Active Students */}
          <div className="rounded-lg border border-gray-200 bg-white p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Students</p>
                <p className="text-2xl font-bold text-gray-900">{stats!.activeStudents}</p>
                {/* You can add a trend indicator here if you have one */}
              </div>
              <div className="rounded-full bg-green-100 p-3">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          {/* Completion Rate */}
          <div className="rounded-lg border border-gray-200 bg-white p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                <p className="text-2xl font-bold text-gray-900">{stats!.completionRate}%</p>
                {/* You can add a trend indicator here if you have one */}
              </div>
              <div className="rounded-full bg-yellow-100 p-3">
                <CheckCircle className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </div>

          {/* Average Score */}
          <div className="rounded-lg border border-gray-200 bg-white p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average Score</p>
                <p className="text-2xl font-bold text-gray-900">{stats!.averageScore}%</p>
                {/* You can add a trend indicator here if you have one */}
              </div>
              <div className="rounded-full bg-purple-100 p-3">
                <Award className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Row */}
        <div  className="grid gap-6 mb-6 md:grid-cols-3">
          {/* Recent Exams */}
          <div className="md:col-span-2 rounded-lg border border-gray-200 bg-white p-6">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Recent Exams</h3>
              <button className="text-sm font-medium text-blue-600 hover:text-blue-700">View All</button>
            </div>
            <RecentExamsTable />
          </div>

          {/* Grade Distribution */}
          <div className="rounded-lg border border-gray-200 bg-white p-6">
            <div className="mb-4 flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Grade Distribution</h3>
                <p className="text-sm text-gray-600">Current semester grade breakdown</p>
              </div>
              <button className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2 py-1 text-xs font-medium text-gray-700 hover:bg-gray-50">
                This Semester
                <ChevronDown className="h-3 w-3" />
              </button>
            </div>
            <GradeDistribution />
          </div>

          
        </div>

        {/* Charts Row */}
        <div className="mb-6 grid gap-6 ">
          {/* Quick Actions */}
          <div className="rounded-lg border border-gray-200 bg-white p-6">
            <h3 className="mb-4 text-lg font-semibold text-gray-900">Quick Actions</h3>
            <div className="space-x-3 flex items-center">
              <button className="flex w-full items-center gap-3 rounded-md border border-gray-200 p-3 text-left hover:bg-gray-50">
                <div className="rounded bg-blue-100 p-2">
                  <ClipboardList className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Create New Exam</p>
                  <p className="text-xs text-gray-500">Set up a new examination</p>
                </div>
              </button>
              <button className="flex w-full items-center gap-3 rounded-md border border-gray-200 p-3 text-left hover:bg-gray-50">
                <div className="rounded bg-green-100 p-2">
                  <BookOpen className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Question Bank</p>
                  <p className="text-xs text-gray-500">Manage your questions</p>
                </div>
              </button>
              <button className="flex w-full items-center gap-3 rounded-md border border-gray-200 p-3 text-left hover:bg-gray-50">
                <div className="rounded bg-purple-100 p-2">
                  <Award className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Grade Exams</p>
                  <p className="text-xs text-gray-500">Review pending results</p>
                </div>
              </button>
              <button className="flex w-full items-center gap-3 rounded-md border border-gray-200 p-3 text-left hover:bg-gray-50">
                <div className="rounded bg-yellow-100 p-2">
                  <Calendar className="h-4 w-4 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Schedule Exam</p>
                  <p className="text-xs text-gray-500">Set exam dates & times</p>
                </div>
              </button>
            </div>
          </div>
          
        </div> 

          
      </main>
    </div>
  );
}
