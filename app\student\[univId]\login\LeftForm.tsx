"use client"
import Link from "next/link"
import { BookOpen, EyeOff, Mail, Lock, ArrowRight, ArrowLeft, GraduationCap, Loader } from "lucide-react"
import { Suspense, useState } from "react"
import { toast } from "sonner";
import { redirect } from "next/navigation";

const tailwindColors = [
  "bg-red-600", "bg-red-700", "bg-gray-800", "bg-blue-600", "bg-blue-800",
  "bg-orange-600", "bg-blue-700", "bg-red-800", "bg-orange-700", "bg-green-600",
  "bg-purple-700", "bg-teal-600", "bg-yellow-500"
];

function generateLogo(name: string): string {
  const words = name.split(" ");
  if (words.length === 1) return words[0].slice(0, 3).toUpperCase();
  return words.slice(0, 2).map(word => word[0].toUpperCase()).join("");
}

function getRandomColor(): string {
  return tailwindColors[Math.floor(Math.random() * tailwindColors.length)];
}

function LoginContent({univID ,univName}:{univID:string,univName:string}) {
  // In a real app, you'd get these from URL params
  const universityName = univName // This would come from URL params
  const role = "student" // This would come from URL params

  const [loading,setLoading] = useState(false)
  const  [formData,setFormData] = useState({email:'',password:''})

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    const {email,password} = formData
    if(email == '' || password==''){
      toast.error('Incomplte Field detected')
    }
    const res = await fetch(`/api/student/${univID}/login`,{
      method:'POST',
      headers:{
        'Content-Type': 'application/json'
      },
      body:JSON.stringify({email,password})
    })
    const data = await res.json()
    if(res.ok){
      toast.success(data.message)
      redirect(`/student/${univID}/dashboard`)
    }
    else{
      toast.error(data.error)
    }
    setLoading(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Side - Login Form */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Back to University Selection */}
          <div className="text-center">
            <Link
              href="/univ/all-university"
              className="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-500 mb-6"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Change University
            </Link>
          </div>

          {/* Logo and Header */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-600 text-white">
                <BookOpen className="h-6 w-6" />
              </div>
              <span className="text-2xl font-bold text-green-600">ExamPro</span>
            </div>

            {/* University and Role Info */}
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-center gap-2 mb-2">
                <div className={`flex h-8 w-8 items-center justify-center rounded ${getRandomColor()} text-white text-sm font-bold`}>
                  {generateLogo(universityName)}
                </div>
                <span className="font-medium text-gray-900">{universityName}</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                {role === "student" ? (
                  <>
                    <BookOpen className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-green-700">Student Login</span>
                  </>
                ) : (
                  <>
                    <GraduationCap className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-blue-700">Student Login</span>
                  </>
                )}
              </div>
            </div>

            <h2 className="text-3xl font-bold text-gray-900">Welcome back</h2>
            <p className="mt-2 text-sm text-gray-600">Sign in to your {role} account</p>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="mt-8 space-y-6">
            <div className="space-y-4">
              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  {role === "student" ? "Student Email" : "Student Email"}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    onChange={(e)=> setFormData(prev => ({...prev,email:e.target.value}))}
                    className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                    placeholder={`Enter your ${role} email`}
                  />
                </div>
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    required
                    onChange={(e)=> setFormData(prev => ({...prev,password:e.target.value}))}
                    className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                    placeholder="Enter your password"
                  />
                  <button type="button" className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </button>
                </div>
              </div>
            </div>

            {/* Remember me and Forgot password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <Link href="/forgot-password" className="font-medium text-green-600 hover:text-green-500">
                  Forgot your password?
                </Link>
              </div>
            </div>

            {/* Sign in button */}
            <div>
              <button
              type="submit"
              disabled={loading}
                className="group disabled:bg-green-400 relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
              >
                Sign in as Student
                {
                  loading ? <Loader size={16} className="mr-2 animate-spin duration-300" /> : <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                }
              </button>
            </div>

            {/* Divider */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
              </div>
            </div>

           

            {/* Sign up link */}
            <div className="text-center">
              <p className="text-sm text-gray-600">
                Don't have an account?{" "}
                <Link href="signup" className="font-medium text-green-600 hover:text-green-500">
                  Sign up for free
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>

      {/* Right Side - Image/Illustration */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0 bg-gradient-to-br from-green-600 to-green-800">
          <div className="flex items-center justify-center h-full p-12">
            <div className="text-center text-white">
              <div className="mb-8">
                <div className="inline-flex items-center justify-center w-24 h-24 bg-white/10 rounded-full mb-6">
                   <GraduationCap className="h-12 w-12" />
                </div>
                <h2 className="text-3xl font-bold mb-4">
                   "Excel in Your Studies"
                </h2>
                <p className="text-xl text-blue-100 max-w-md">
                   Take exams, track your progress, and achieve academic success with ExamPro.
                </p>
              </div>
              <div className="grid grid-cols-1 gap-4 max-w-sm mx-auto">
                
                  <>
                    <div className="flex items-center gap-3 text-blue-100">
                      <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
                      <span>Take exams anywhere</span>
                    </div>
                    <div className="flex items-center gap-3 text-blue-100">
                      <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
                      <span>Instant results</span>
                    </div>
                    <div className="flex items-center gap-3 text-blue-100">
                      <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
                      <span>Progress tracking</span>
                    </div>
                    <div className="flex items-center gap-3 text-blue-100">
                      <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
                      <span>Study resources</span>
                    </div>
                  </>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function LoginPage({univID,univName}: {univID: string,univName: string}) {
  return (
      <LoginContent univID={univID} univName={univName} />
  )
}
