"use client"

import { useState, useEffect } from "react"
import {
  BookOpen,
  Search,
  Plus,
  Upload,
  MoreHorizontal,
  Eye,
  Edit,
  RotateCcw,
  UserX,
  UserCheck,
  Mail,
  Download,
  ChevronDown,
  Bell,
  HelpCircle,
  CheckSquare,
  Square,
  X,
} from "lucide-react"

// Database-driven students data

const courses = ["Advanced Mathematics", "Physics", "Chemistry", "Biology", "Computer Science"]

export default function StudentsPage({ params }: { params: { univID: string } }) {
  const [searchTerm, setSearchTerm] = useState("")
  const [courseFilter, setCourseFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [showAddStudent, setShowAddStudent] = useState(false)
  const [showBulkImport, setShowBulkImport] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<any>(null)

  // Database state
  const [students, setStudents] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch students from database
  const fetchStudents = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(`/api/admin/${params.univID}/students`)
      if (!response.ok) {
        throw new Error('Failed to fetch students')
      }
      const data = await response.json()
      setStudents(data.students || [])
    } catch (err: any) {
      setError(err.message)
      setStudents([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStudents()
  }, [params.univID])

  const filteredStudents = students.filter((student) => {
    const matchesSearch =
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (student.studentId && student.studentId.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCourse = courseFilter === "all" || (student.courses && student.courses.includes(courseFilter))
    const matchesStatus = statusFilter === "all" || student.status === statusFilter
    return matchesSearch && matchesCourse && matchesStatus
  })

  const handleSelectAll = () => {
    if (selectedStudents.length === filteredStudents.length) {
      setSelectedStudents([])
    } else {
      setSelectedStudents(filteredStudents.map((s) => s.id))
    }
  }

  const handleSelectStudent = (studentId: number) => {
    setSelectedStudents((prev) =>
      prev.includes(studentId) ? prev.filter((id) => id !== studentId) : [...prev, studentId],
    )
  }

  const formatLastLogin = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold text-gray-900">Students</h1>
            </div>
            <div className="flex items-center gap-4">
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <Bell className="h-5 w-5" />
              </button>
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <HelpCircle className="h-5 w-5" />
              </button>
              <div className="flex gap-2">
                <button
                  onClick={() => setShowBulkImport(true)}
                  className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                >
                  <Upload className="h-4 w-4" />
                  Bulk Import
                </button>
                <button
                  onClick={() => setShowAddStudent(true)}
                  className="inline-flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium"
                >
                  <Plus className="h-4 w-4" />
                  Add Student
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* Filters and Search */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by name, email, or student ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex gap-3">
                {/* Course Filter */}
                <div className="relative">
                  <select
                    value={courseFilter}
                    onChange={(e) => setCourseFilter(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="all">All Courses</option>
                    {courses.map((course) => (
                      <option key={course} value={course}>
                        {course}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Status Filter */}
                <div className="relative">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Export Button */}
                <button className="inline-flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors font-medium">
                  <Download className="h-4 w-4" />
                  Export CSV
                </button>
              </div>
            </div>
          </div>

          {/* Bulk Actions Bar */}
          {selectedStudents.length > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium text-green-900">
                    {selectedStudents.length} student{selectedStudents.length > 1 ? "s" : ""} selected
                  </span>
                  <button onClick={() => setSelectedStudents([])} className="text-green-600 hover:text-green-800 text-sm">
                    Clear selection
                  </button>
                </div>
                <div className="flex gap-2">
                  <button className="inline-flex items-center gap-2 bg-green-600 text-white px-3 py-1.5 rounded-md hover:bg-green-700 transition-colors text-sm">
                    <Mail className="h-4 w-4" />
                    Send Message
                  </button>
                  <button className="inline-flex items-center gap-2 bg-gray-600 text-white px-3 py-1.5 rounded-md hover:bg-gray-700 transition-colors text-sm">
                    <UserX className="h-4 w-4" />
                    Deactivate
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Results Summary */}
          <div className="mb-6">
            <p className="text-sm text-gray-600">
              Showing {filteredStudents.length} of {mockStudents.length} students
            </p>
          </div>

          {/* Students Table */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-left py-3 px-6">
                      <button onClick={handleSelectAll} className="flex items-center">
                        {selectedStudents.length === filteredStudents.length && filteredStudents.length > 0 ? (
                          <CheckSquare className="h-4 w-4 text-green-600" />
                        ) : (
                          <Square className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Student
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Student ID
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Courses
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Status
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Last Login
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Performance
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {filteredStudents.map((student) => (
                    <StudentRow
                      key={student.id}
                      student={student}
                      isSelected={selectedStudents.includes(student.id)}
                      onSelect={() => handleSelectStudent(student.id)}
                      onViewProfile={() => setSelectedStudent(student)}
                      formatLastLogin={formatLastLogin}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>

      {/* Modals */}
      {showAddStudent && <AddStudentModal onClose={() => setShowAddStudent(false)} />}
      {showBulkImport && <BulkImportModal onClose={() => setShowBulkImport(false)} />}
      {selectedStudent && <StudentProfileModal student={selectedStudent} onClose={() => setSelectedStudent(null)} />}
    </div>
  )
}

function StudentRow({
  student,
  isSelected,
  onSelect,
  onViewProfile,
  formatLastLogin,
}: {
  student: any
  isSelected: boolean
  onSelect: () => void
  onViewProfile: () => void
  formatLastLogin: (date: string) => string
}) {
  const [showDropdown, setShowDropdown] = useState(false)

  return (
    <tr className="hover:bg-gray-50">
      <td className="py-4 px-6">
        <button onClick={onSelect}>
          {isSelected ? (
            <CheckSquare className="h-4 w-4 text-green-600" />
          ) : (
            <Square className="h-4 w-4 text-gray-400" />
          )}
        </button>
      </td>
      <td className="py-4 px-6">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 text-green-600 text-sm font-medium">
            {student.name.charAt(0)}
          </div>
          <div>
            <div className="text-sm font-medium text-gray-900">{student.name}</div>
            <div className="text-sm text-gray-500">{student.email}</div>
          </div>
        </div>
      </td>
      <td className="py-4 px-6 text-sm text-gray-900">{student.studentId}</td>
      <td className="py-4 px-6">
        <div className="flex flex-wrap gap-1">
          {student.courses.map((course:any) => (
            <span
              key={course}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
            >
              {course}
            </span>
          ))}
        </div>
      </td>
      <td className="py-4 px-6">
        <span
          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            student.status === "active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
          }`}
        >
          {student.status === "active" ? <UserCheck className="h-3 w-3 mr-1" /> : <UserX className="h-3 w-3 mr-1" />}
          {student.status.charAt(0).toUpperCase() + student.status.slice(1)}
        </span>
      </td>
      <td className="py-4 px-6 text-sm text-gray-900">{formatLastLogin(student.lastLogin)}</td>
      <td className="py-4 px-6">
        <div className="text-sm">
          <div className="font-medium text-gray-900">{student.averageScore}%</div>
          <div className="text-gray-500">{student.examsCompleted} exams</div>
        </div>
      </td>
      <td className="py-4 px-6">
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            className="p-1 rounded hover:bg-gray-100 transition-colors"
          >
            <MoreHorizontal className="h-4 w-4 text-gray-500" />
          </button>
          {showDropdown && (
            <div className="absolute right-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
              <div className="py-1">
                <button
                  onClick={onViewProfile}
                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <Eye className="h-4 w-4" />
                  View Profile
                </button>
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                  <Edit className="h-4 w-4" />
                  Edit Student
                </button>
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                  <Mail className="h-4 w-4" />
                  Send Message
                </button>
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                  <RotateCcw className="h-4 w-4" />
                  Reset Password
                </button>
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                  <UserX className="h-4 w-4" />
                  Deactivate
                </button>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  )
}

function AddStudentModal({ onClose }: { onClose: () => void }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Add New Student</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Enter student's full name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Enter student's email"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Student ID</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Enter student ID"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Course Assignment</label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
              <option value="">Select a course</option>
              {courses.map((course) => (
                <option key={course} value={course}>
                  {course}
                </option>
              ))}
            </select>
          </div>
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button type="submit" className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
              Add Student
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

function BulkImportModal({ onClose }: { onClose: () => void }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Bulk Import Students</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="space-y-4">
          <div>
            <p className="text-sm text-gray-600 mb-3">
              Upload a CSV file with student information. The file should include columns for Name, Email, Student ID,
              and Course.
            </p>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600">Drop your CSV file here or click to browse</p>
              <input type="file" accept=".csv" className="hidden" />
            </div>
          </div>
          <div className="bg-green-50 rounded-md p-3">
            <h4 className="text-sm font-medium text-green-800 mb-1">CSV Format Requirements:</h4>
            <ul className="text-xs text-green-700 space-y-1">
              <li>• Name, Email, Student ID, Course (required columns)</li>
              <li>• Email addresses must be unique</li>
              <li>• Student IDs must be unique</li>
              <li>• Course must match existing course names</li>
            </ul>
          </div>
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button type="submit" className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
              Import Students
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

function StudentProfileModal({ student, onClose }: { student: any; onClose: () => void }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Student Profile</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Basic Info */}
          <div className="flex items-start gap-4">
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-100 text-green-600 text-xl font-medium">
              {student.name.charAt(0)}
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900">{student.name}</h3>
              <p className="text-gray-600">{student.email}</p>
              <p className="text-sm text-gray-500">Student ID: {student.studentId}</p>
              <p className="text-sm text-gray-500">Enrolled: {new Date(student.enrollmentDate).toLocaleDateString()}</p>
            </div>
            <span
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                student.status === "active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
              }`}
            >
              {student.status.charAt(0).toUpperCase() + student.status.slice(1)}
            </span>
          </div>

          {/* Course Enrollments */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Course Enrollments</h4>
            <div className="flex flex-wrap gap-2">
              {student.courses.map((course: string) => (
                <span
                  key={course}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                >
                  {course}
                </span>
              ))}
            </div>
          </div>

          {/* Performance Metrics */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Performance Overview</h4>
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-gray-900">{student.averageScore}%</div>
                <div className="text-sm text-gray-600">Average Score</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-gray-900">{student.examsCompleted}</div>
                <div className="text-sm text-gray-600">Exams Completed</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-gray-900">A-</div>
                <div className="text-sm text-gray-600">Current Grade</div>
              </div>
            </div>
          </div>

          {/* Recent Exam History */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Recent Exam History</h4>
            <div className="space-y-2">
              {[
                { exam: "Advanced Mathematics Midterm", score: 92, date: "2024-01-15", status: "Completed" },
                { exam: "Physics Quiz #3", score: 88, date: "2024-01-10", status: "Completed" },
                { exam: "Chemistry Lab Test", score: 85, date: "2024-01-05", status: "Completed" },
              ].map((exam, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium text-gray-900">{exam.exam}</div>
                    <div className="text-sm text-gray-600">{new Date(exam.date).toLocaleDateString()}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-gray-900">{exam.score}%</div>
                    <div className="text-sm text-green-600">{exam.status}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4 border-t">
            <button className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors">
              <Mail className="h-4 w-4 inline mr-2" />
              Send Message
            </button>
            <button className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors">
              <Edit className="h-4 w-4 inline mr-2" />
              Edit Profile
            </button>
            <button className="flex-1 bg-yellow-100 text-yellow-700 py-2 px-4 rounded-md hover:bg-yellow-200 transition-colors">
              <RotateCcw className="h-4 w-4 inline mr-2" />
              Reset Password
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
