'use client'
import Link from "next/link"
import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { BookOpen } from "lucide-react";

export default function PaymentCallbackPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'failed' | 'cancelled'>('loading');
  const [paymentDetails, setPaymentDetails] = useState<any>(null);
  const [error, setError] = useState('');

  const searchParams = useSearchParams();
  const txRef = searchParams.get('tx_ref');
  const transactionId = searchParams.get('transaction_id');
  const statusParam = searchParams.get('status');

  useEffect(() => {
    const verifyPayment = async () => {
      if (statusParam === 'cancelled' || statusParam === 'canceled') {
        setStatus('cancelled');
        setError('You cancelled the payment. No charges were made to your account.');
        setTimeout(() => {
          window.location.href = '/payment?message=Payment was cancelled. Please try again.';
        }, 3000);
        return;
      }

      if (!txRef) {
        setStatus('failed');
        setError('Missing transaction reference');
        return;
      }

      try {
        const response = await fetch('/api/payment/verify', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ tx_ref: txRef, transaction_id: transactionId }),
        });
        const data = await response.json();
        if (data.success) {
          setStatus('success');
          console.log(data.data)
          setPaymentDetails(data.data);
        } else {
          setStatus('failed');
          setError(data.error || 'Payment verification failed');
        }
      } catch (err) {
        console.error('Payment verification error:', err);
        setStatus('failed');
        setError('Failed to verify payment');
      }
    };
    verifyPayment();
  }, [txRef, transactionId, statusParam]);

  const getStatusIcon = () => {
    switch (status) {
      case 'success': return '✅';
      case 'failed': return '❌';
      case 'cancelled': return '⚠️';
      default: return '⏳';
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'loading': return 'Verifying your payment...';
      case 'success': return 'Payment Successful!';
      case 'failed': return 'Payment Failed';
      case 'cancelled': return 'Payment Cancelled';
      default: return 'Processing...';
    }
  };

  return (
    <div className="min-h-screen w-full bg-gray-50">
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-green-600 text-white">
                <BookOpen className="h-4 w-4" />
              </div>
              <Link href="/" className="text-xl font-bold text-green-600">ExamPro</Link>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/help" className="text-sm text-gray-600 hover:text-gray-900">Need help?</Link>
              <Link href="/contact" className="text-sm font-medium text-green-600 hover:text-green-700">Contact Support</Link>
            </div>
          </div>
        </div>
      </header>

      <div className="flex items-center justify-center py-12 px-4">
        <div className="max-w-lg w-full bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">

          <div className={`px-8 py-6 ${
            status === 'success' ? 'bg-gradient-to-r from-green-600 to-emerald-600' :
            status === 'failed' ? 'bg-gradient-to-r from-red-600 to-red-700' :
            status === 'cancelled' ? 'bg-gradient-to-r from-yellow-500 to-amber-600' :
            'bg-gradient-to-r from-blue-600 to-indigo-600'
          }`}>
            <div className="text-center">
              <div className="text-6xl mb-4">{getStatusIcon()}</div>
              <h1 className="text-2xl font-bold text-white mb-2">{getStatusMessage()}</h1>
              {status === 'loading' && (
                <div className="flex justify-center">
                  <svg className="animate-spin h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              )}
            </div>
          </div>

          <div className="p-8">
                {txRef && (
              <div className="mt-8 pt-6 border-t border-gray-200 text-center">
                <p className="text-xs text-gray-500 mb-1">Transaction Token</p>
                <div className="w-full flex items-center">
                  <input type="text" value={paymentDetails?.token} readOnly className="w-full px-4 py-2 my-2 outline-none bg-gray-50 rounded-md" />
                  <button  className="ml-2 text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
            {status === 'success' && paymentDetails && (
              <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-6">
                <h3 className="font-semibold text-green-800 mb-4 flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  Payment Details
                </h3>
                <div className="grid grid-cols-1 gap-3">
                  <div className="flex justify-between py-2 border-b border-green-200">
                    <span className="font-medium text-green-700">Amount:</span>
                    <span className="text-green-800">{paymentDetails.amount} {paymentDetails.currency}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b border-green-200">
                    <span className="font-medium text-green-700">Transaction ID:</span>
                    <span className="text-green-800 font-mono text-sm">{paymentDetails.flw_ref}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b border-green-200">
                    <span className="font-medium text-green-700">Reference:</span>
                    <span className="text-green-800 font-mono text-sm">{paymentDetails.tx_ref}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b border-green-200">
                    <span className="font-medium text-green-700">Method:</span>
                    <span className="text-green-800 capitalize">{paymentDetails.payment_type}</span>
                  </div>
                  <div className="flex justify-between py-2">
                    <span className="font-medium text-green-700">Date:</span>
                    <span className="text-green-800">{new Date(paymentDetails.created_at).toLocaleString()}</span>
                  </div>
                </div>
              </div>
            )}

            {status === 'failed' && (
              <div className="bg-red-50 border border-red-200 rounded-xl p-6 mb-6">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <h3 className="font-semibold text-red-800 mb-2">Payment Failed</h3>
                    <p className="text-red-700 text-sm">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {status === 'cancelled' && (
              <div className="bg-amber-50 border border-amber-200 rounded-xl p-6 mb-6">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-amber-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <h3 className="font-semibold text-amber-800 mb-2">Payment Cancelled</h3>
                    <p className="text-amber-700 text-sm">You cancelled the payment. No charges were made.</p>
                  </div>
                </div>
              </div>
            )}

            {status === 'loading' && (
              <div className="text-center py-8">
                <p className="text-gray-600 mb-4">Please wait while we verify your payment...</p>
                <div className="flex justify-center">
                  <div className="animate-pulse flex space-x-1">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-4">
              {status === 'success' && (
                <>
                  <Link href="/admin/subscriptions" className="block w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold py-3 rounded-xl text-center">View Subscription</Link>
                  <Link href="/admin/dashboard" className="block w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 rounded-xl text-center border border-gray-300">Go to Dashboard</Link>
                </>
              )}

              {(status === 'failed' || status === 'cancelled') && (
                <>
                  <Link href="/payment" className="block w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 rounded-xl text-center">Try Payment Again</Link>
                  <Link href="/" className="block w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 rounded-xl text-center border border-gray-300">Go to Homepage</Link>
                </>
              )}
            </div>

            

            <div className="mt-6 text-center">
              <p className="text-xs text-gray-500 flex items-center justify-center">
                <svg className="w-3 h-3 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                Secured by 256-bit SSL encryption
              </p>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
}
