import { redirect } from "next/navigation";
import Results from "./Result";
import { verifyStudentSession } from "@/app/lib/student-session";

const page = async ({params}:{params:Promise<{univId:string}>}) => {
  const {univId} = await params;
  const session = await verifyStudentSession();
  if(!session) {
    redirect('/student/' + univId + '/login');
  }
  const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/student/${univId}/${session.id}/result`);
  
  if(!res.ok) {
    throw new Error('Failed to fetch results');
  }
  
  const results = await res.json();
  
  return (
      <Results results={Array.isArray(results) ? results : []} />
    
  );
}

export default page;