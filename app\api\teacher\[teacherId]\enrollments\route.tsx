// app/api/teacher/[teacherId]/enrollments/route.ts
import  prisma  from '@/app/lib/prisma'
import { NextResponse } from 'next/server'

export async function GET(req: Request, { params }: { params: Promise<{ teacherId: string }> }) {
    const { teacherId } = await params
   
    const enrollments = await prisma.examEnrollment.findMany({
    where: {
      exam: {
        instructorId: teacherId
      }
    },
    include: {
      student: { select: { id: true, name: true } },
      exam: { select: { id: true, title: true } }
    },
    orderBy: { enrolledAt: 'desc' }
  })

  return NextResponse.json(enrollments)
}
