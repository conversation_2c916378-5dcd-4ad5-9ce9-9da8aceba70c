import { NextRequest, NextResponse } from 'next/server'
import { differenceInMinutes, isAfter, isBefore, isWithinInterval } from 'date-fns'
import prisma from '@/app/lib/prisma'

export async function GET(req: NextRequest,context: { params: Promise<{ univId: string, studentId: string }> }) {
  
const { univId, studentId } = await context.params
  if (!univId || !studentId) {
    return NextResponse.json({ error: 'Missing univId or studentId' }, { status: 400 })
  }

  try {
    const exams = await prisma.exam.findMany({
      where: {
        course: {
          department: {
            univId,
          },
        },
      },
      include: {
        instructor: true,
        questions: true,
        enrollments: {
          where: { studentId },
        },
        sessions: {
          include: {
            students: {
              where: { studentId },
            },
          },
        },
      },
    })

    const formatted = exams.map((exam) => {
      const now = new Date()
        // Parse exam start date and time

      const [hours, minutes] = exam.startTime.split(':').map(Number)

        // Combine date and time into a Date object
        const start = new Date(exam.startDate)
        start.setHours(hours)
        start.setMinutes(minutes)
        start.setSeconds(0)
        start.setMilliseconds(0)

const durationInMinutes = exam.duration
const end = new Date(start.getTime() + durationInMinutes * 60000)

let status: 'upcoming' | 'ongoing' | 'completed' = 'upcoming'
      if (isBefore(now, start)) status = 'upcoming'
      else if (isWithinInterval(now, { start, end })) status = 'ongoing'
      else if (isAfter(now, end)) status = 'completed'

      // Student enrollment check
      const enrolled:('approve' | 'pending' | 'reject'|null) = exam.enrollments.length > 0 ? exam.enrollments[0].status as ('approve' | 'pending' | 'reject') : null

      // Optional score (only if completed and session+grade exists)
      const studentSession = exam.sessions.flatMap(s => s.students).find(s => s.studentId === studentId)

      return {
        id: exam.id,
        title: exam.title,
        status,
        date: exam.startDate.toISOString(),
        duration: exam.duration,
        totalQuestions: exam.questions.length,
        passingScore: 70, // Default, or you can add a column for this
        enrolled,
        instructor: exam.instructor.name,
        description: exam.description,
        ...(status === 'completed' && studentSession?.grade ? { score: studentSession.grade } : {})
      }
    })

    return NextResponse.json(formatted)
  } catch (error) {
    console.error('[EXAMS_API]', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
