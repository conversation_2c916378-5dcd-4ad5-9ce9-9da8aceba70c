import { verifyStudentSession } from "@/app/lib/student-session";
import { redirect } from "next/navigation";
import Dashboard from "./Dashboard";
import { checkAndStartExamSessions } from "@/app/student/action";

const getExam = async (univId:string,studentId:string) => {
  const [res,checkSession] = await Promise.all([
    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/student/${univId}/${studentId}/exam`),
    checkAndStartExamSessions()
  ]);

  console.log('checkSession',checkSession)
  
  if(!res.ok) {
    throw new Error('Failed to fetch exams');
    return []
  }
  return await res.json()
}

const page = async ({params}:{params:Promise<{univId:string}>}) => {
  const {univId} = await params;
  const session = await verifyStudentSession();
  if(!session) {
    redirect('/student/' + univId + '/login');
  }
  const exams = await getExam(univId,session.id)

  return(
    <Dashboard univId={univId} exams={Array.isArray(exams) ? exams : []}  studentId={session.id} />
  )
  
}

export default page