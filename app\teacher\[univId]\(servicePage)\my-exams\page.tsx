import { verifyInstructorSession } from '@/app/lib/instructor-session'
import { Abyssinica_SIL } from 'next/font/google'
import { redirect } from 'next/navigation'
import React from 'react'
import MyExamsPage from './display'

type Props = {}

const page = async (props: Props) => {
  const instructor = await verifyInstructorSession()
  if(!instructor) redirect('/')
  const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/teacher/${instructor.id}/exams`)
  const data = await res.json()
  console.log(data)
  return (
    <MyExamsPage Data={data.exams}/>
  )
}

export default page