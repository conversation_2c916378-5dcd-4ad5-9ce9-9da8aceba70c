
"use client"
import { Copy } from 'lucide-react'
import React from 'react'

type Props = {
    code:string
}
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }
const CopuButton = ({code}: Props) => {
  return (
   <button
            onClick={() => copyToClipboard(code)}
            className="p-1 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded"
          >
            <Copy className="h-4 w-4" />
          </button>
  )
}

export default CopuButton