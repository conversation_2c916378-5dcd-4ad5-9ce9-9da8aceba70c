// File: app/api/exam-sessions/route.ts
import prisma from '@/app/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(req: NextRequest,context: { params: Promise<{ univId: string, studentId: string }> }) {
    const { univId } = await context.params
  if (!univId ) {
    return NextResponse.json({ error: 'Missing univId or studentId' }, { status: 400 })
  }

  try {
    const sessions = await prisma.examSession.findMany({
      where: {
        exam: {
          course: {
            department: {
              univId,
            },
          },
        },
      },
      include: {
        exam: {
          select: { title: true },
        },
      },
      orderBy: { startedAt: 'desc' },
    })

    const result = sessions.map(s => ({
      id: s.id,
      examTitle: s.exam.title,
      startTime: s.startedAt.toISOString(),
      status: s.status as 'active' | 'completed',
    }))

    return NextResponse.json(result)
  } catch (error) {
    console.error('EXAM_SESSIONS_GET_ERROR', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
