"use server"
import {SignJWT,jwtVerify} from 'jose'
import { cookies } from 'next/headers'
const key =  new TextEncoder().encode(process.env.JWT_SECRET)

const cookie = {
    name: 'instructor_token_session',
    options: { httpOnly: true, secure: true, sameSite: 'lax' as 'lax', path: '/' },
    duration: 10 * 60 * 1000 // 10 minutes for token session
}
type InstructorTokenSessionData = {
    token: string,
    univId: string,
    univName?: string
}
export async function createInstructorTokenSession(data: InstructorTokenSessionData) {
    const expires = new Date(Date.now() + cookie.duration)
    const session = await new SignJWT(data)
        .setProtectedHeader({alg:'HS256'})
        .setIssuedAt()
        .setExpirationTime('10m')
        .sign(key)
    const cookieStore = await cookies();
    cookieStore.set(cookie.name, session, { ...cookie.options, expires });
}

export async function verifyInstructorTokenSession() {
    const cookieStore = await cookies();
    const session = cookieStore.get(cookie.name)?.value;
    if(!session) return null
    try {
        const {payload} = await jwtVerify(session, key, { algorithms:['HS256'] })
        return payload as InstructorTokenSessionData
    } catch (error) {
        return null
    }
}

export async function deleteInstructorTokenSession() {
    const cookieStore = await cookies();
    cookieStore.delete(cookie.name);
}
