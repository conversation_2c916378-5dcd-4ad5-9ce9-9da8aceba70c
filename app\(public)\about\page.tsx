"use client"

import React, { useState, useEffect } from 'react'
import { GraduationCap, ArrowLeft, Github, Linkedin, Mail, Code, Database, Palette, Shield, Users, Trophy, Star, Zap, Heart, Eye } from 'lucide-react'

const teamMembers = [
  {
    id: 1,
    name: "<PERSON><PERSON> Destin Tresor",
    role: "Group Leader & Scrum Master",
    email: "<EMAIL>",
    image: "/team/nedjou-destin.jpg",
    bio: "Led the development team and managed project coordination using Scrum methodology to ensure efficient delivery.",
    contributions: [
      "Project leadership and coordination",
      "Scrum methodology implementation",  
      "Team management and communication",
      "Sprint planning and retrospectives",
      "Stakeholder communication"
    ],
    technologies: ["Scrum", "Agile", "Project Management", "Team Leadership", "Communication"],
    github: "https://github.com/nedjoudestin",
    linkedin: "https://linkedin.com/in/nedjoudestin",
    color: "from-purple-500 to-indigo-600"
  },
  {
    id: 2,
    name: "Ndassa Njoya Fils Fayssal",
    role: "Backend Developer",
    email: "<EMAIL>",
    image: "/team/ndassa-fayssal.jpg",
    bio: "Developed and maintained the backend infrastructure, API endpoints, and database architecture.",
    contributions: [
      "Backend API development",
      "Database design and optimization",
      "Server-side logic implementation",
      "Authentication and security",
      "Performance optimization"
    ],
    technologies: ["Node.js", "Prisma", "PostgreSQL", "JWT", "API Development"],
    github: "https://github.com/ndassafayssal",
    linkedin: "https://linkedin.com/in/ndassafayssal",
    color: "from-blue-500 to-cyan-600"
  },
  {
    id: 3,
    name: "Abdel-kader Bilal Alamine",
    role: "Frontend Developer",
    email: "<EMAIL>",
    image: "/team/abdel-bilal.jpg",
    bio: "Designed and implemented user interfaces with focus on user experience and responsive design.",
    contributions: [
      "Frontend component development",
      "User interface design",
      "Responsive web design",
      "State management implementation",
      "User experience optimization"
    ],
    technologies: ["React", "Next.js", "TypeScript", "Tailwind CSS", "JavaScript"],
    github: "https://github.com/abdelbilal",
    linkedin: "https://linkedin.com/in/abdelbilal",
    color: "from-green-500 to-emerald-600"
  },
  {
    id: 4,
    name: "Ngoko Tantchou Chris Manuel",
    role: "Frontend Developer",
    email: "<EMAIL>",
    image: "/team/ngoko-chris.jpg",
    bio: "Specialized in creating interactive user interfaces and implementing modern frontend technologies.",
    contributions: [
      "Interactive UI components",
      "Frontend architecture design",
      "Cross-browser compatibility",
      "Performance optimization",
      "Code quality assurance"
    ],
    technologies: ["React", "Next.js", "CSS", "HTML", "JavaScript"],
    github: "https://github.com/ngokochris",
    linkedin: "https://linkedin.com/in/ngokochris",
    color: "from-orange-500 to-red-600"
  },
  {
    id: 5,
    name: "Chofor Seitsou Priestley Clarkson",
    role: "Technical Support Specialist",
    email: "<EMAIL>",
    image: "/team/chofor-priestley.jpg",
    bio: "Provided technical support, system maintenance, and user assistance throughout the development process.",
    contributions: [
      "Technical support and troubleshooting",
      "System maintenance and monitoring",
      "User assistance and training",
      "Bug reporting and testing",
      "Documentation support"
    ],
    technologies: ["System Administration", "Troubleshooting", "User Support", "Testing", "Documentation"],
    github: "https://github.com/choforpriestley",
    linkedin: "https://linkedin.com/in/choforpriestley",
    color: "from-pink-500 to-rose-600"
  },
  {
    id: 6,
    name: "Mbuna Verlaine Claude",
    role: "Product Owner",
    email: "<EMAIL>",
    image: "/team/mbuna-verlaine.jpg",
    bio: "Managed product requirements, stakeholder communication, and ensured alignment with business objectives.",
    contributions: [
      "Product vision and strategy",
      "Requirements gathering and analysis",
      "Stakeholder communication",
      "Feature prioritization",
      "User story creation"
    ],
    technologies: ["Product Management", "Requirements Analysis", "Stakeholder Management", "Agile", "Business Analysis"],
    github: "https://github.com/mbunaverlaine",
    linkedin: "https://linkedin.com/in/mbunaverlaine",
    color: "from-violet-500 to-purple-600"
  }
]

const projectStats = [
  { label: "Lines of Code", value: "25,000+", icon: Code },
  { label: "Components Built", value: "150+", icon: Palette },
  { label: "API Endpoints", value: "45+", icon: Database },
  { label: "Database Tables", value: "12", icon: Shield },
  { label: "Test Cases", value: "300+", icon: Trophy },
  { label: "Development Hours", value: "2,000+", icon: Zap }
]

const AnimatedCounter = ({ end, duration = 2000 }: { end: number; duration?: number }) => {
  const [count, setCount] = useState(0)
  
  useEffect(() => {
    let start = 0
    const increment = end / (duration / 16)
    const timer = setInterval(() => {
      start += increment
      setCount(Math.floor(start))
      if (start >= end) {
        setCount(end)
        clearInterval(timer)
      }
    }, 16)
    
    return () => clearInterval(timer)
  }, [end, duration])
  
  return count
}

const FloatingElement = ({ children, delay = 0 }: { children: React.ReactNode; delay?: number }) => {
  return (
    <div 
      className="animate-bounce"
      style={{ 
        animationDelay: `${delay}s`,
        animationDuration: '3s',
        animationIterationCount: 'infinite'
      }}
    >
      {children}
    </div>
  )
}

export default function AboutPage() {
  const [hoveredMember, setHoveredMember] = useState<number | null>(null)
  const [scrollY, setScrollY] = useState(0)

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY)
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 backdrop-blur-md bg-white/10 border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2 group cursor-pointer">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-r from-green-400 to-blue-500 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
                ExamPro
              </span>
            </div>
            <button className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-white/10 backdrop-blur-md border border-white/20 rounded-xl hover:bg-white/20 transition-all duration-300 hover:scale-105 group">
              <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
              Back to Home
            </button>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div 
            className="transform transition-transform duration-1000"
            style={{ transform: `translateY(${scrollY * 0.1}px)` }}
          >
            <h1 className="text-6xl font-bold bg-gradient-to-r from-white via-blue-100 to-green-100 bg-clip-text text-transparent mb-6 leading-tight">
              About Our Platform
            </h1>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto mb-8 leading-relaxed">
              Built by a dedicated team of developers and designers from ICT University,
              this platform represents the future of secure online examination systems.
            </p>
            <div className="flex justify-center space-x-2">
              <FloatingElement delay={0}>
                <Star className="h-6 w-6 text-yellow-400" />
              </FloatingElement>
              <FloatingElement delay={0.5}>
                <Heart className="h-6 w-6 text-red-400" />
              </FloatingElement>
              <FloatingElement delay={1}>
                <Zap className="h-6 w-6 text-blue-400" />
              </FloatingElement>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mb-16">
          <div className="backdrop-blur-md bg-white/10 rounded-3xl border border-white/20 p-8 max-w-6xl mx-auto shadow-2xl">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
              {projectStats.map((stat, index) => {
                const Icon = stat.icon
                return (
                  <div key={index} className="text-center group">
                    <div className="bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="text-3xl font-bold text-white mb-2">
                      <AnimatedCounter end={parseInt(stat.value.replace(/\D/g, ''))} />
                      {stat.value.includes('+') && '+'}
                    </div>
                    <div className="text-sm text-gray-300">{stat.label}</div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Mission Section */}
        <div className="mb-16">
          <div className="backdrop-blur-md bg-white/10 rounded-3xl border border-white/20 p-8 max-w-5xl mx-auto shadow-2xl">
            <div className="text-center mb-8">
              <h2 className="text-4xl font-bold text-white mb-4">Our Mission</h2>
              <Eye className="h-8 w-8 text-blue-400 mx-auto" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl p-6 border border-white/10 hover:border-white/30 transition-colors duration-300">
                <Shield className="h-8 w-8 text-blue-400 mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">
                  Secure & Reliable Testing
                </h3>
                <p className="text-gray-300 leading-relaxed">
                  We believe that online examinations should be as secure and reliable as
                  traditional in-person testing, while offering the convenience and
                  accessibility of digital platforms.
                </p>
              </div>
              <div className="bg-gradient-to-br from-green-500/20 to-blue-500/20 rounded-2xl p-6 border border-white/10 hover:border-white/30 transition-colors duration-300">
                <Trophy className="h-8 w-8 text-green-400 mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">
                  Educational Excellence
                </h3>
                <p className="text-gray-300 leading-relaxed">
                  Our platform empowers educators to focus on what matters most -
                  teaching and assessment - while we handle the technical complexities
                  of secure online examination delivery.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">Meet Our Team</h2>
            <p className="text-gray-300 text-lg">
              The talented individuals who brought this platform to life
            </p>
            <Users className="h-8 w-8 text-purple-400 mx-auto mt-4" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member) => (
              <div 
                key={member.id} 
                className="group relative"
                onMouseEnter={() => setHoveredMember(member.id)}
                onMouseLeave={() => setHoveredMember(null)}
              >
                <div className={`backdrop-blur-md bg-white/10 rounded-3xl border border-white/20 hover:border-white/40 transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 shadow-xl hover:shadow-2xl ${hoveredMember === member.id ? 'shadow-2xl scale-105 -translate-y-2' : ''}`}>
                  <div className="p-6">
                    <div className="flex items-start gap-4 mb-4">
                      <div className={`flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r ${member.color} text-white text-lg font-bold shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-xl font-semibold text-white group-hover:text-blue-100 transition-colors">
                          {member.name}
                        </h3>
                        <p className={`text-sm font-medium mt-1 bg-gradient-to-r ${member.color} bg-clip-text text-transparent`}>
                          {member.role}
                        </p>
                      </div>
                    </div>

                    <p className="text-sm text-gray-300 mb-4 leading-relaxed">{member.bio}</p>

                    <div className="mb-4">
                      <h4 className="font-semibold text-white mb-2 text-sm">Key Contributions:</h4>
                      <ul className="text-xs text-gray-300 space-y-1">
                        {member.contributions.slice(0, 3).map((contribution, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-blue-400 mr-2">•</span>
                            {contribution}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-semibold text-white mb-2 text-sm">Technologies:</h4>
                      <div className="flex flex-wrap gap-2">
                        {member.technologies.slice(0, 4).map((tech, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-white/20 text-white text-xs rounded-lg backdrop-blur-sm"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="flex justify-center space-x-4">
                      <a
                        href={`mailto:${member.email}`}
                        className="p-2 rounded-xl bg-white/10 text-gray-300 hover:text-white hover:bg-white/20 transition-all duration-300 hover:scale-110"
                        title={`Email ${member.name}`}
                      >
                        <Mail className="h-5 w-5" />
                      </a>
                      <a
                        href={member.github}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-xl bg-white/10 text-gray-300 hover:text-white hover:bg-white/20 transition-all duration-300 hover:scale-110"
                        title={`${member.name}'s GitHub Profile`}
                      >
                        <Github className="h-5 w-5" />
                      </a>
                      <a
                        href={member.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-xl bg-white/10 text-gray-300 hover:text-white hover:bg-white/20 transition-all duration-300 hover:scale-110"
                        title={`${member.name}'s LinkedIn Profile`}
                      >
                        <Linkedin className="h-5 w-5" />
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Technology Stack */}
        <div className="mb-16">
          <div className="backdrop-blur-md bg-white/10 rounded-3xl border border-white/20 p-8 max-w-5xl mx-auto shadow-2xl">
            <div className="text-center mb-8">
              <h2 className="text-4xl font-bold text-white mb-4">Technology Stack</h2>
              <p className="text-gray-300 text-lg">
                Built with modern, reliable technologies for optimal performance
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { icon: Code, title: "Frontend", desc: "Next.js, React, TypeScript, Tailwind CSS", color: "from-green-400 to-blue-500" },
                { icon: Database, title: "Backend", desc: "Node.js, Prisma ORM, PostgreSQL", color: "from-blue-400 to-purple-500" },
                { icon: Shield, title: "Security", desc: "NextAuth.js, bcrypt, JWT, WebRTC", color: "from-purple-400 to-pink-500" },
                { icon: Palette, title: "Design", desc: "Tailwind CSS, Lucide Icons, Custom Components", color: "from-pink-400 to-red-500" }
              ].map((tech, index) => (
                <div key={index} className="text-center group hover:scale-105 transition-transform duration-300">
                  <div className={`bg-gradient-to-r ${tech.color} rounded-2xl p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                    <tech.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="font-semibold text-white mb-2 text-lg">{tech.title}</h3>
                  <p className="text-sm text-gray-300 leading-relaxed">{tech.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="text-center">
          <div className="backdrop-blur-md bg-white/10 rounded-3xl border border-white/20 p-8 max-w-md mx-auto shadow-2xl">
            <h3 className="text-2xl font-semibold text-white mb-4">Get In Touch</h3>
            <p className="text-sm text-gray-300 mb-6 leading-relaxed">
              This project was developed as part of our Software Engineering coursework at ICT University.
              We're always open to feedback and collaboration opportunities.
            </p>
            <div className="space-y-4">
              <button className="w-full bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 px-6 rounded-xl hover:from-green-600 hover:to-blue-700 transition-all duration-300 text-sm font-medium inline-flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                <Mail className="h-4 w-4" />
                Contact Team
              </button>
              <button className="w-full border border-white/30 bg-white/10 backdrop-blur-md text-white py-3 px-6 rounded-xl hover:bg-white/20 transition-all duration-300 text-sm font-medium inline-flex items-center justify-center gap-2 hover:scale-105">
                <Github className="h-4 w-4" />
                View Source
              </button>
            </div>
          </div>
        </div>
      </main>

      <style jsx>{`
        @keyframes blob {
          0% { transform: translate(0px, 0px) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
          100% { transform: translate(0px, 0px) scale(1); }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
    </div>
  )
}