import { NextResponse } from "next/server"
import prisma from "@/app/lib/prisma"
import { verifyInstructorSession } from "@/app/lib/instructor-session"

// Create Exam
export async function POST(req: Request,context: { params: Promise<{ teacherId: string }> }) {
  const { teacherId } = await context.params
  const data = await req.json()
  try {
    const exam = await prisma.exam.create({
      data: {
        title: data.title,
        description: data.description,
        duration: data.duration,
        startDate: new Date(data.startDate),
        startTime: data.startTime,
        instructorId: teacherId,
        courseId: data.courseId, // NEW
        questions: {
          create: data.questions.map((q: any) => ({
            text: q.text,
            type: q.type,
            required: q.required,
            options: q.options,
            saved: q.saved ?? false,
            savedAt: q.savedAt ? new Date(q.savedAt) : undefined,
          })),
        },
      },
      include: { questions: true },
    })
    return NextResponse.json(exam)
  } catch (e:any) {
    return NextResponse.json({ error: "Failed to create exam", details: e.message }, { status: 500 })
  }
}

// Update Exam
export async function PUT(req: Request,{params:{teacherId}}:{params:{teacherId:string}}  ) {
  
  const data = await req.json()
  try {
    const exam = await prisma.exam.update({
      where: { id: data.id },
      data: {
        title: data.title,
        description: data.description,
        duration: data.duration,
        startDate: new Date(data.startDate),
        startTime: data.startTime,
        questions: {
          deleteMany: {},
          create: data.questions.map((q: any) => ({
            text: q.text,
            type: q.type,
            required: q.required,
            options: q.options,
            saved: q.saved ?? false,
            savedAt: q.savedAt ? new Date(q.savedAt) : undefined,
          })),
        },
      },
      include: { questions: true },
    })
    return NextResponse.json(exam)
  } catch (e:any) {
    return NextResponse.json({ error: "Failed to update exam", details: e.message }, { status: 500 })
  }
}

// Delete Exam
export async function DELETE(req: Request) {
  
  const { id } = await req.json()
  try {
    await prisma.exam.delete({ where: { id } })
    return NextResponse.json({ success: true })
  } catch (e:any) {
    return NextResponse.json({ error: "Failed to delete exam", details: e.message }, { status: 500 })
  }
}
