import { NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifyInstructorSession } from '@/app/lib/instructor-session';

// POST: Bulk import students
export async function POST(request: Request) {
  try {
    const instructor = await verifyInstructorSession();
    if (!instructor) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { students, univId } = body;

    if (!Array.isArray(students) || students.length === 0) {
      return NextResponse.json(
        { error: 'Students array is required and cannot be empty' },
        { status: 400 }
      );
    }

    if (!univId) {
      return NextResponse.json(
        { error: 'University ID is required' },
        { status: 400 }
      );
    }

    const results = {
      successful: [],
      failed: [],
      duplicates: []
    };

    // Process each student
    for (const studentData of students) {
      const { name, email, password } = studentData;

      // Validate required fields
      if (!name || !email || !password) {
        results.failed.push({
          email: email || 'unknown',
          error: 'Missing required fields: name, email, password'
        });
        continue;
      }

      try {
        // Check if student already exists
        const existingStudent = await prisma.student.findUnique({
          where: { email }
        });

        if (existingStudent) {
          results.duplicates.push({
            email,
            name,
            message: 'Student already exists'
          });
          continue;
        }

        // Create new student
        const newStudent = await prisma.student.create({
          data: {
            name,
            email,
            univId,
            password, // In production, hash this password
            instructor: {
              connect: { id: instructor.id }
            },
            status: 'active',
            lastLogin: new Date(),
            joinDate: new Date()
          },
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
            joinDate: true
          }
        });

        results.successful.push(newStudent);

      } catch (error) {
        console.error(`Error creating student ${email}:`, error);
        results.failed.push({
          email,
          name,
          error: 'Database error during creation'
        });
      }
    }

    return NextResponse.json({
      message: 'Bulk import completed',
      summary: {
        total: students.length,
        successful: results.successful.length,
        failed: results.failed.length,
        duplicates: results.duplicates.length
      },
      results
    }, { status: 201 });

  } catch (error) {
    console.error('Error in bulk import:', error);
    return NextResponse.json(
      { error: 'Failed to process bulk import' },
      { status: 500 }
    );
  }
}

// PUT: Bulk update students
export async function PUT(request: Request) {
  try {
    const instructor = await verifyInstructorSession();
    if (!instructor) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { updates } = body;

    if (!Array.isArray(updates) || updates.length === 0) {
      return NextResponse.json(
        { error: 'Updates array is required and cannot be empty' },
        { status: 400 }
      );
    }

    const results = {
      successful: [],
      failed: []
    };

    // Process each update
    for (const updateData of updates) {
      const { id, name, email, status } = updateData;

      if (!id) {
        results.failed.push({
          id: 'unknown',
          error: 'Student ID is required'
        });
        continue;
      }

      try {
        // Verify student belongs to instructor
        const existingStudent = await prisma.student.findFirst({
          where: {
            id,
            instructor: { some: { id: instructor.id } }
          }
        });

        if (!existingStudent) {
          results.failed.push({
            id,
            error: 'Student not found or not accessible'
          });
          continue;
        }

        // Update student
        const updatedStudent = await prisma.student.update({
          where: { id },
          data: {
            ...(name && { name }),
            ...(email && { email }),
            ...(status && { status }),
            updatedAt: new Date()
          },
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
            updatedAt: true
          }
        });

        results.successful.push(updatedStudent);

      } catch (error) {
        console.error(`Error updating student ${id}:`, error);
        results.failed.push({
          id,
          error: 'Database error during update'
        });
      }
    }

    return NextResponse.json({
      message: 'Bulk update completed',
      summary: {
        total: updates.length,
        successful: results.successful.length,
        failed: results.failed.length
      },
      results
    });

  } catch (error) {
    console.error('Error in bulk update:', error);
    return NextResponse.json(
      { error: 'Failed to process bulk update' },
      { status: 500 }
    );
  }
}

// DELETE: Bulk deactivate students
export async function DELETE(request: Request) {
  try {
    const instructor = await verifyInstructorSession();
    if (!instructor) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { studentIds } = body;

    if (!Array.isArray(studentIds) || studentIds.length === 0) {
      return NextResponse.json(
        { error: 'Student IDs array is required and cannot be empty' },
        { status: 400 }
      );
    }

    const results = {
      successful: [],
      failed: []
    };

    // Process each deletion
    for (const studentId of studentIds) {
      try {
        // Verify student belongs to instructor
        const existingStudent = await prisma.student.findFirst({
          where: {
            id: studentId,
            instructor: { some: { id: instructor.id } }
          }
        });

        if (!existingStudent) {
          results.failed.push({
            id: studentId,
            error: 'Student not found or not accessible'
          });
          continue;
        }

        // Soft delete by changing status
        await prisma.student.update({
          where: { id: studentId },
          data: {
            status: 'inactive',
            updatedAt: new Date()
          }
        });

        results.successful.push({
          id: studentId,
          name: existingStudent.name,
          email: existingStudent.email
        });

      } catch (error) {
        console.error(`Error deactivating student ${studentId}:`, error);
        results.failed.push({
          id: studentId,
          error: 'Database error during deactivation'
        });
      }
    }

    return NextResponse.json({
      message: 'Bulk deactivation completed',
      summary: {
        total: studentIds.length,
        successful: results.successful.length,
        failed: results.failed.length
      },
      results
    });

  } catch (error) {
    console.error('Error in bulk delete:', error);
    return NextResponse.json(
      { error: 'Failed to process bulk deactivation' },
      { status: 500 }
    );
  }
}
