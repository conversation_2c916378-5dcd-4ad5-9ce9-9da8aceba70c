import { NextResponse } from 'next/server';
import { MonitoringService } from '@/lib/monitoring-service';

const monitoringService = MonitoringService.getInstance();

export async function GET() {
  try {
    const metrics = await monitoringService.getMetrics();
    
    return NextResponse.json(metrics);
  } catch (error: any) {
    console.error('Monitoring endpoint failed:', error);
    return NextResponse.json(
      {
        error: 'Failed to get monitoring data',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { action } = await req.json();

    if (action === 'health-check') {
      const health = await monitoringService.checkSystemHealth();
      return NextResponse.json(health);
    }

    if (action === 'test-alerts') {
      // Only allow in development
      if (process.env.NODE_ENV === 'production') {
        return NextResponse.json(
          { error: 'Test alerts not available in production' },
          { status: 403 }
        );
      }

      // Simulate a test alert
      const testAlert = {
        severity: 'warning' as const,
        service: 'test_service',
        status: 'degraded' as const,
        error: 'This is a test alert',
        responseTime: 5500,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
      };

      // This would trigger the alert system
      console.log('Test alert triggered:', testAlert);
      
      return NextResponse.json({
        success: true,
        message: 'Test alert triggered',
        alert: testAlert,
      });
    }

    return NextResponse.json(
      { error: 'Invalid action. Use "health-check" or "test-alerts"' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Monitoring action failed:', error);
    return NextResponse.json(
      {
        error: 'Monitoring action failed',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
