import { NextResponse } from 'next/server';
import { PaymentService } from '@/lib/payment-service';
import prisma from '@/app/lib/prisma';

const paymentService = new PaymentService();

export async function POST(req: Request) {
  try {
    const { email, amount, subscriptionPlan, phoneNumber, customerName } = await req.json();

    // Validate required fields
    if (!email || !amount || !subscriptionPlan) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // For now, we'll use a simple plan mapping until Prisma client is regenerated
   
    const plan = await prisma.plan.findFirst({where:{billing:subscriptionPlan,isActive:true}})
    if (!plan) {
      return NextResponse.json(
        { success: false, error: 'Invalid subscription plan' },
        { status: 400 }
      );
    }

    // Initiate payment through Flutterwave
    const paymentResult = await paymentService.initiatePayment({
      email,
      amount: parseFloat(amount.toString()),
      currency: 'XAF',
      method: 'flutterwave', // All payments go through Flutterwave
      planId: plan.id,
      phoneNumber,
      orderId: `order_${Date.now()}`,
      customerName,
    });

    if (!paymentResult.success) {
      return NextResponse.json(
        { success: false, error: paymentResult.error },
        { status: 400 }
      );
    }

    // Note: Receipt email will be sent after successful payment verification
    // This happens in the webhook handler or payment verification endpoint

    return NextResponse.json({
      success: true,
      paymentId: paymentResult.paymentId,
      paymentUrl: paymentResult.paymentUrl,
      transactionId: paymentResult.transactionId,
      token: paymentResult.paymentId, // for backward compatibility
      message: 'Payment initiated successfully. You will be redirected to complete the payment.',
    });
  } catch (error: any) {
    console.error('Payment API error:', error);
    return NextResponse.json(
      { success: false, error: 'Payment processing failed' },
      { status: 500 }
    );
  }
}
