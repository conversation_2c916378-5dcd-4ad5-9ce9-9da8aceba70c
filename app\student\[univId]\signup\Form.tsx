"use client"

import type React from "react"
import { useEffect, useState } from "react"

import Link from "next/link"
import { <PERSON><PERSON>pen, EyeOff, Mail, Lock, User, Building, ArrowRight, Check } from "lucide-react"
import { toast } from "sonner"
import { redirect } from "next/navigation"

export default function SignupPage({univID,univName}:{univID: string,univName: string}) {
  const [isCreating, setIsCreating] = useState(false)

  // Signup Form Submission
  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);
    const form = e.target as HTMLFormElement;
    const fullName = (form.elements.namedItem('fullName') as HTMLInputElement).value;
    const email = (form.elements.namedItem('email') as HTMLInputElement).value;
    const password = (form.elements.namedItem('password') as HTMLInputElement).value;
    // Add confirm password check if needed
    const res = await fetch(`/api/student/${univID}/signup`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name: fullName, email, password })
    });
    const data = await res.json();
    setIsCreating(false);
    if (data.success) {
      toast.success('Account created!');
      redirect('login')
    } else {
      toast.error(data.error || 'Signup failed.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex overflow-hidden">
      {/* Left Side - Forms Container */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-md w-full">
          {/* Signup Form Only */}
          <div className="relative w-full">
            <div className="w-full">
              <div className="space-y-8">
                {/* Logo and Header */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-6">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-600 text-white">
                      <BookOpen className="h-6 w-6" />
                    </div>
                    <span className="text-2xl font-bold text-green-600">ExamPro</span>
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900">Student Registration</h2>
                  <p className="mt-2 text-sm text-gray-600">Create your student account for {univName}</p>
                </div>

                {/* Signup Form */}
                <form className="mt-8 space-y-6" onSubmit={handleSignup}>
                  <div className="space-y-4">
                    {/* Full Name Field */}
                    <div>
                      <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="fullName"
                          name="fullName"
                          type="text"
                          autoComplete="name"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Enter your full name"
                        />
                      </div>
                    </div>

                    {/* Email Field */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email address
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Mail className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="email"
                          name="email"
                          type="email"
                          autoComplete="email"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Enter your email"
                        />
                      </div>
                    </div>

                    {/* Institution Field */}
                    <div>
                      <label htmlFor="institution" className="block text-sm font-medium text-gray-700 mb-1">
                        Institution
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Building className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="institution"
                          name="institution"
                          type="text"
                          required
                          value={univName}
                          disabled
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Enter your institution name"
                        />
                      </div>
                    </div>

                    {/* Password Field */}
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        Password
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="password"
                          name="password"
                          type="password"
                          autoComplete="new-password"
                          required
                          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Create a password"
                        />
                        <button type="button" className="absolute inset-y-0 right-0 pr-3 flex items-center">
                          <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </button>
                      </div>
                    </div>

                    {/* Confirm Password Field */}
                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="confirmPassword"
                          name="confirmPassword"
                          type="password"
                          autoComplete="new-password"
                          required
                          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Confirm your password"
                        />
                        <button type="button" className="absolute inset-y-0 right-0 pr-3 flex items-center">
                          <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Password Requirements */}
                  <div className="bg-gray-50 rounded-md p-3">
                    <p className="text-xs font-medium text-gray-700 mb-2">Password must contain:</p>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>At least 8 characters</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>One uppercase letter</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>One number</span>
                      </div>
                    </div>
                  </div>

                  {/* Terms and Privacy */}
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="terms"
                        name="terms"
                        type="checkbox"
                        required
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="terms" className="text-gray-700">
                        I agree to the{" "}
                        <Link href="/terms" className="font-medium text-green-600 hover:text-green-500">
                          Terms of Service
                        </Link>{" "}
                        and{" "}
                        <Link href="/privacy" className="font-medium text-green-600 hover:text-green-500">
                          Privacy Policy
                        </Link>
                      </label>
                    </div>
                  </div>

                  {/* Create account button */}
                  <div>
                    <button
                    type="submit"
                    disabled={isCreating}
                      className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isCreating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Creating...
                        </>
                      ) : (
                        <>
                          Create account
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </>
                      )}
                    </button>
                  </div>

                  {/* Sign in link */}
                  <div className="text-center">
                    <p className="text-sm text-gray-600">
                      Already have an account?{" "}
                      <Link href={`/student/${univID}/login`} className="font-medium text-green-600 hover:text-green-500">
                        Sign in
                      </Link>
                    </p>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Image/Illustration */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0 bg-gradient-to-br from-green-600 to-green-600">
          <div className="flex items-center justify-center h-full p-12">
            <div className="text-center text-white">
              <div className="mb-8">
                <div className="inline-flex items-center justify-center w-24 h-24 bg-white/10 rounded-full mb-6">
                  <BookOpen className="h-12 w-12" />
                </div>
                <h2 className="text-3xl font-bold mb-4">
                  Start Your Learning Journey
                </h2>
                <p className="text-xl text-green-100 max-w-md">
                  Create your student account to access exams, track your progress, and connect with your institution.
                </p>
              </div>
              <div className="grid grid-cols-1 gap-4 max-w-sm mx-auto">
                <div className="flex items-center gap-3 text-green-100">
                  <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                  <span>Free to get started</span>
                </div>
                <div className="flex items-center gap-3 text-green-100">
                  <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                  <span>Access all your exams</span>
                </div>
                <div className="flex items-center gap-3 text-green-100">
                  <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                  <span>Real-time results</span>
                </div>
                <div className="flex items-center gap-3 text-green-100">
                  <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                  <span>24/7 support</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
