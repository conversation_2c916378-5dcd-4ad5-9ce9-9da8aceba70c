import { NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifyInstructorSession } from '@/app/lib/instructor-session';

// GET: Student performance analytics
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const instructor = await verifyInstructorSession();
    if (!instructor) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const studentId = params.id;
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '30'; // days
    const includeComparison = searchParams.get('comparison') === 'true';

    // Verify student belongs to instructor
    const student = await prisma.student.findFirst({
      where: {
        id: studentId,
        instructor: { some: { id: instructor.id } }
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      );
    }

    const timeframeDate = new Date();
    timeframeDate.setDate(timeframeDate.getDate() - parseInt(timeframe));

    // Get student's exam sessions within timeframe
    const examSessions = await prisma.examSession.findMany({
      where: {
        studentId: studentId,
        exam: { instructorId: instructor.id },
        startedAt: { gte: timeframeDate }
      },
      select: {
        id: true,
        score: true,
        status: true,
        startedAt: true,
        endedAt: true,
        exam: {
          select: {
            id: true,
            title: true,
            createdAt: true
          }
        }
      },
      orderBy: { startedAt: 'asc' }
    });

    const completedSessions = examSessions.filter(session => session.status === 'completed');
    const scores = completedSessions.map(session => session.score || 0);

    // Calculate performance metrics
    const performanceMetrics = {
      totalExams: examSessions.length,
      completedExams: completedSessions.length,
      averageScore: scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0,
      highestScore: scores.length > 0 ? Math.max(...scores) : 0,
      lowestScore: scores.length > 0 ? Math.min(...scores) : 0,
      standardDeviation: calculateStandardDeviation(scores),
      completionRate: examSessions.length > 0 ? (completedSessions.length / examSessions.length) * 100 : 0,
      improvementRate: calculateImprovementRate(scores)
    };

    // Calculate time-based performance trends
    const performanceTrends = calculatePerformanceTrends(completedSessions);

    // Grade distribution
    const gradeDistribution = calculateGradeDistribution(scores);

    // Performance comparison with class average (if requested)
    let classComparison = null;
    if (includeComparison) {
      const classAverage = await calculateClassAverage(instructor.id, timeframeDate);
      classComparison = {
        studentAverage: performanceMetrics.averageScore,
        classAverage: classAverage,
        percentile: await calculateStudentPercentile(studentId, instructor.id, timeframeDate)
      };
    }

    // Strengths and weaknesses analysis
    const subjectAnalysis = await analyzeSubjectPerformance(studentId, instructor.id, timeframeDate);

    return NextResponse.json({
      student: {
        id: student.id,
        name: student.name,
        email: student.email
      },
      timeframe: parseInt(timeframe),
      performanceMetrics,
      performanceTrends,
      gradeDistribution,
      classComparison,
      subjectAnalysis,
      recentExams: completedSessions.slice(-10),
      recommendations: generateRecommendations(performanceMetrics, performanceTrends)
    });

  } catch (error) {
    console.error('Error fetching student performance:', error);
    return NextResponse.json(
      { error: 'Failed to fetch student performance' },
      { status: 500 }
    );
  }
}

// Helper functions
function calculateStandardDeviation(scores: number[]): number {
  if (scores.length === 0) return 0;
  const mean = scores.reduce((a, b) => a + b, 0) / scores.length;
  const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
  return Math.sqrt(variance);
}

function calculateImprovementRate(scores: number[]): number {
  if (scores.length < 2) return 0;
  const firstHalf = scores.slice(0, Math.ceil(scores.length / 2));
  const secondHalf = scores.slice(Math.ceil(scores.length / 2));
  
  const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
  const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
  
  return ((secondAvg - firstAvg) / firstAvg) * 100;
}

function calculatePerformanceTrends(sessions: any[]): any {
  const weeklyData = groupSessionsByWeek(sessions);
  const monthlyData = groupSessionsByMonth(sessions);
  
  return {
    weekly: weeklyData,
    monthly: monthlyData,
    trend: calculateTrendDirection(sessions.map(s => s.score || 0))
  };
}

function calculateGradeDistribution(scores: number[]): any {
  const distribution = {
    'A (90-100)': 0,
    'B (80-89)': 0,
    'C (70-79)': 0,
    'D (60-69)': 0,
    'F (0-59)': 0
  };

  scores.forEach(score => {
    if (score >= 90) distribution['A (90-100)']++;
    else if (score >= 80) distribution['B (80-89)']++;
    else if (score >= 70) distribution['C (70-79)']++;
    else if (score >= 60) distribution['D (60-69)']++;
    else distribution['F (0-59)']++;
  });

  return distribution;
}

async function calculateClassAverage(instructorId: string, fromDate: Date): Promise<number> {
  const result = await prisma.examSession.aggregate({
    where: {
      exam: { instructorId },
      startedAt: { gte: fromDate },
      status: 'completed'
    },
    _avg: { score: true }
  });
  
  return result._avg.score || 0;
}

async function calculateStudentPercentile(studentId: string, instructorId: string, fromDate: Date): Promise<number> {
  const studentAvg = await prisma.examSession.aggregate({
    where: {
      studentId,
      exam: { instructorId },
      startedAt: { gte: fromDate },
      status: 'completed'
    },
    _avg: { score: true }
  });

  const allStudentAvgs = await prisma.examSession.groupBy({
    by: ['studentId'],
    where: {
      exam: { instructorId },
      startedAt: { gte: fromDate },
      status: 'completed'
    },
    _avg: { score: true }
  });

  const studentScore = studentAvg._avg.score || 0;
  const lowerScores = allStudentAvgs.filter(avg => (avg._avg.score || 0) < studentScore).length;
  
  return (lowerScores / allStudentAvgs.length) * 100;
}

async function analyzeSubjectPerformance(studentId: string, instructorId: string, fromDate: Date): Promise<any> {
  // This would require exam categorization by subject
  // For now, return a placeholder structure
  return {
    strengths: ['Mathematics', 'Problem Solving'],
    weaknesses: ['Time Management', 'Complex Analysis'],
    recommendations: ['Focus on practice problems', 'Improve time allocation']
  };
}

function groupSessionsByWeek(sessions: any[]): any[] {
  // Group sessions by week and calculate averages
  const weeks: { [key: string]: number[] } = {};
  
  sessions.forEach(session => {
    const week = getWeekKey(new Date(session.startedAt));
    if (!weeks[week]) weeks[week] = [];
    if (session.score) weeks[week].push(session.score);
  });

  return Object.entries(weeks).map(([week, scores]) => ({
    week,
    average: scores.reduce((a, b) => a + b, 0) / scores.length,
    count: scores.length
  }));
}

function groupSessionsByMonth(sessions: any[]): any[] {
  // Similar to weekly grouping but by month
  const months: { [key: string]: number[] } = {};
  
  sessions.forEach(session => {
    const month = getMonthKey(new Date(session.startedAt));
    if (!months[month]) months[month] = [];
    if (session.score) months[month].push(session.score);
  });

  return Object.entries(months).map(([month, scores]) => ({
    month,
    average: scores.reduce((a, b) => a + b, 0) / scores.length,
    count: scores.length
  }));
}

function calculateTrendDirection(scores: number[]): string {
  if (scores.length < 2) return 'insufficient_data';
  
  const firstHalf = scores.slice(0, Math.ceil(scores.length / 2));
  const secondHalf = scores.slice(Math.ceil(scores.length / 2));
  
  const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
  const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
  
  const difference = secondAvg - firstAvg;
  
  if (difference > 5) return 'improving';
  if (difference < -5) return 'declining';
  return 'stable';
}

function getWeekKey(date: Date): string {
  const year = date.getFullYear();
  const week = Math.ceil((date.getDate() - date.getDay() + 1) / 7);
  return `${year}-W${week}`;
}

function getMonthKey(date: Date): string {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
}

function generateRecommendations(metrics: any, trends: any): string[] {
  const recommendations = [];
  
  if (metrics.averageScore < 60) {
    recommendations.push('Consider additional study sessions and practice materials');
  }
  
  if (metrics.completionRate < 80) {
    recommendations.push('Focus on improving exam completion rate');
  }
  
  if (trends.trend === 'declining') {
    recommendations.push('Schedule a one-on-one meeting to address performance concerns');
  }
  
  if (metrics.standardDeviation > 20) {
    recommendations.push('Work on consistency in performance across different topics');
  }
  
  return recommendations;
}
