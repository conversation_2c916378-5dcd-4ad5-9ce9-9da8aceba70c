import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifySession } from '@/app/lib/session';

// GET: Fetch all students for a university
export async function GET(req: NextRequest, { params }: { params: { univId: string } }) {
  const user = await verifySession();
  if (!user) {
    return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }
  const { univId } = params;
  try {
    const students = await prisma.student.findMany({
      where: { univId },
      select: {
        id: true,
        name: true,
        email: true,
        status: true,
        lastLogin: true,
        joinDate: true,
        examsCompleted: true,
        averageScore: true,
      },
      orderBy: { createdAt: 'desc' },
    });
    return NextResponse.json({ success: true, students });
  } catch (error) {
    return NextResponse.json({ success: false, error: 'Failed to fetch students' }, { status: 500 });
  }
}

// PATCH: Block or unblock a student
export async function PATCH(req: NextRequest, { params }: { params: { univId: string } }) {
  const user = await verifySession();
  if (!user) {
    return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }
  const { univId } = params;
  const { studentId, action } = await req.json();
  if (!studentId || !['block', 'unblock'].includes(action)) {
    return NextResponse.json({ success: false, error: 'Invalid request' }, { status: 400 });
  }
  try {
    const updated = await prisma.student.update({
      where: { id: studentId, univId },
      data: { status: action === 'block' ? 'blocked' : 'active' },
    });
    return NextResponse.json({ success: true, student: updated });
  } catch (error) {
    return NextResponse.json({ success: false, error: 'Failed to update student status' }, { status: 500 });
  }
}
