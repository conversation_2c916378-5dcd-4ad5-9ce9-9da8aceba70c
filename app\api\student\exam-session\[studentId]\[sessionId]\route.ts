import prisma from "@/app/lib/prisma"
import { NextResponse } from "next/server"

export async function GET(req: Request, context: { params: Promise<{ studentId: string, sessionId: string }> } ) {
  const { studentId, sessionId } = await context.params

  const studentExamSession = await prisma.studentExamSession.findUnique({
    where: { studentId_examSessionId: { studentId, examSessionId:sessionId} },
    include: {
      examSession: {
        include: {
          exam: {
            include: {
              questions: true,
            }
          }
        }
      }
    }
  })

  if (!studentExamSession) {
    return NextResponse.json({ error: "Student exam session not found" }, { status: 404 })
  }

  const exam = studentExamSession.examSession.exam

  const examData = {
    id: exam.id,
    title: exam.title,
    description: exam.description,
    duration: exam.duration,
    startDate: studentExamSession.examSession.startedAt.toISOString().split("T")[0],
    startTime: `${new Date(studentExamSession.examSession.startedAt).getHours().toString().padStart(2, '0')}:${new Date(studentExamSession.examSession.startedAt).getMinutes().toString().padStart(2, '0')}`,
    questions: exam.questions.map(q => ({
      id: q.id,
      text: q.text,
      type: q.type,
      required: q.required,
      options: Array.isArray(q.options)
  ? q.options.map((opt: any) => ({ id: opt.id, text: opt.text }))
  : [],  
    }))
  }

  return NextResponse.json(examData)
}