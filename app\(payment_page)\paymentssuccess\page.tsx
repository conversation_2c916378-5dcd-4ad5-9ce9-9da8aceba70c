// app/paymentssuccess/page.tsx
'use client';
import React from 'react';

interface PaymentSuccessPageProps {
  selectedPlan: { name: string; price: number; billing: string };
  finalPrice: string;
}

const PaymentSuccessPage: React.FC<PaymentSuccessPageProps> = ({ selectedPlan, finalPrice }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex justify-center items-center p-8 font-sans">
      <div className="bg-white rounded-2xl shadow-xl p-10 max-w-lg text-center flex flex-col items-center">
        <svg
          className="w-24 h-24 text-green-500 mb-6 animate-bounce"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h1 className="text-4xl font-extrabold text-gray-900 mb-4">Payment Successful!</h1>
        <p className="text-lg text-gray-700 mb-6">Your subscription to the <strong className="text-indigo-600">{selectedPlan.name}</strong> has been activated.</p>
        <div className="bg-green-50 p-6 rounded-xl w-full max-w-sm mb-8 text-left border border-green-200">
          <h2 className="text-xl font-bold text-gray-800 mb-3">Order Details:</h2>
          <p className="text-gray-700 text-lg mb-2">Plan: <span className="font-semibold">{selectedPlan.name}</span></p>
          <p className="text-gray-700 text-lg mb-2">Billing: <span className="font-semibold">{selectedPlan.billing === 'month' ? 'Monthly' : 'Annual'}</span></p>
          <p className="text-gray-700 text-lg font-bold mt-4 border-t border-dashed border-green-300 pt-3">Total Paid: <span className="text-green-700 text-2xl">${finalPrice}</span></p>
        </div>
        <p className="text-md text-gray-600 mb-8">A confirmation email has been sent to your registered email address.</p>
        <a
          href="/" // Or navigate to a dashboard page
          className="inline-flex items-center px-8 py-3 border border-transparent text-lg font-semibold rounded-full shadow-lg text-white bg-indigo-600 hover:bg-indigo-700 transition-all duration-300 transform hover:scale-105"
        >
          Go to Dashboard
          <svg className="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"></path></svg>
        </a>
      </div>
    </div>
  );
};

export default PaymentSuccessPage;