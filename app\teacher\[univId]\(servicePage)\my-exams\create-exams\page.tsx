import { verifyInstructorSession } from '@/app/lib/instructor-session'
import { redirect } from 'next/navigation'
import React from 'react'
import CreateExam from './Form'

type Props = {
    params:Promise<{univId:string}>
}

const page = async ({params}: Props) => {
    const instructor = await verifyInstructorSession()
    if(!instructor) redirect('/')
    
  return (
    <CreateExam teacherId={instructor.id} />
  )
}

export default page