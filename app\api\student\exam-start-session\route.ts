import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma"; // adjust based on your project setup

export async function POST(req: Request) {
  const { studentId, examId } = await req.json();

  const examSession = await prisma.examSession.findFirst({
    where: { examId },
    select: { id: true },
  });

  if (!examSession) return NextResponse.json({ error: "Exam session not found" }, { status: 404 });

  let studentSession = await prisma.studentExamSession.findFirst({
    where: {
      studentId,
      examSessionId: examSession.id,
    },
  });

  if (!studentSession) {
    studentSession = await prisma.studentExamSession.create({
      data: {
        studentId,
        examSessionId: examSession.id,
      },
    });
  }

  return NextResponse.json({ sessionId: studentSession.examSessionId });
}
