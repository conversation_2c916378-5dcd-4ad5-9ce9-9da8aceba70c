import { NextResponse } from 'next/server';
import { EmailService } from '@/lib/email-service';
import prisma from '@/app/lib/prisma';


export async function GET() {
  try {
    // Only show this in development mode
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Debug endpoint not available in production' },
        { status: 403 }
      );
    }

    // Get email statistics
    const stats = await EmailService.getEmailStats();

    // Get recent payments to show email status
    const recentPayments = await prisma.payment.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10,
    });

    const paymentDetails = recentPayments.map(payment => ({
      id: payment.id,
      email: payment.email,
      amount: payment.amount,
      status: payment.subscription,
      token: payment.token,
      createdAt: payment.createdAt,
      emailSent: payment.subscription === 'completed', // Simplified check
    }));

    return NextResponse.json({
      status: 'success',
      emailStats: stats,
      recentPayments: paymentDetails,
      emailFlow: {
        description: 'Email sending flow',
        steps: [
          '1. User initiates payment → NO EMAIL SENT',
          '2. User completes payment on Flutterwave → NO EMAIL SENT',
          '3. Flutterwave sends webhook OR user returns to callback → EMAIL SENT',
          '4. Payment verification confirms success → EMAIL SENT (if not already sent)',
        ],
        currentBehavior: 'Emails are only sent AFTER successful payment verification',
        duplicatePrevention: 'Emails are not sent if payment is already marked as completed',
      },
      troubleshooting: {
        commonIssues: [
          'Email sent too early → Fixed: Emails only sent after verification',
          'Duplicate emails → Fixed: Check payment status before sending',
          'Email not sent → Check email configuration and logs',
        ],
        emailConfiguration: {
          emailUser: process.env.EMAIL_USER ? 'SET' : 'NOT SET',
          emailPass: process.env.EMAIL_PASS ? 'SET' : 'NOT SET',
          smtpConfigured: !!(process.env.EMAIL_USER && process.env.EMAIL_PASS),
        },
      },
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to get email debug info', details: error.message },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    // Only allow in development mode
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Debug endpoint not available in production' },
        { status: 403 }
      );
    }

    const { action, email, transactionId } = await req.json();

    if (action === 'test-email') {
      // Test sending a receipt email
      const result = await EmailService.sendPaymentConfirmationEmail(
        email || '<EMAIL>',
        transactionId || 'test_' + Date.now(),
        50000,
        'XAF',
        'Test Subscription Plan'
      );

      return NextResponse.json({
        success: result.success,
        message: result.message,
        testEmail: email || '<EMAIL>',
        testTransactionId: transactionId || 'test_' + Date.now(),
      });
    }

    if (action === 'check-email-status') {
      if (!transactionId) {
        return NextResponse.json(
          { error: 'Transaction ID required for email status check' },
          { status: 400 }
        );
      }

      const emailSent = await EmailService.wasReceiptEmailSent(transactionId);
      
      return NextResponse.json({
        transactionId,
        emailSent,
        message: emailSent 
          ? 'Receipt email was already sent for this transaction'
          : 'No receipt email sent for this transaction',
      });
    }

    return NextResponse.json(
      { error: 'Invalid action. Use "test-email" or "check-email-status"' },
      { status: 400 }
    );
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Email debug action failed', details: error.message },
      { status: 500 }
    );
  }
}
