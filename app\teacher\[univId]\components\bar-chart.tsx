"use client"

import { useEffect, useRef } from "react"
import { Circle } from "lucide-react"

export function BarChart() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    // Data for the chart
    const months = ["Oct", "Nov", "Dec"]
    const data = [
      { month: "Oct", values: [2000, 1800, 1600, 1400] },
      { month: "Nov", values: [1700, 1500, 1300, 1100] },
      { month: "Dec", values: [2200, 2000, 1800, 1600] },
    ]

    // Colors for the bars
    const colors = ["#4f46e5", "#3b82f6", "#06b6d4", "#14b8a6"]

    // Chart dimensions
    const chartWidth = canvas.width - 40
    const chartHeight = canvas.height - 40
    const barGroupWidth = chartWidth / months.length
    const barWidth = barGroupWidth * 0.15
    const barSpacing = barWidth * 0.5

    // Draw the chart
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Draw the bars
    data.forEach((monthData, monthIndex) => {
      const x = monthIndex * barGroupWidth + 20

      monthData.values.forEach((value, valueIndex) => {
        const barX = x + valueIndex * (barWidth + barSpacing)
        const barHeight = (value / 2500) * chartHeight
        const barY = canvas.height - barHeight - 20

        ctx.fillStyle = colors[valueIndex]
        ctx.beginPath()
        ctx.roundRect(barX, barY, barWidth, barHeight, 4)
        ctx.fill()
      })

      // Draw month label
      ctx.fillStyle = "#6b7280"
      ctx.font = "12px Inter, sans-serif"
      ctx.textAlign = "center"
      ctx.fillText(monthData.month, x + barGroupWidth / 2, canvas.height - 5)
    })
  }, [])

  return (
    <div className="h-[180px] w-full">
      <canvas ref={canvasRef} className="h-full w-full"></canvas>
      <div className="mt-2 flex items-center justify-center gap-4">
        <div className="flex items-center gap-1">
          <Circle className="h-2 w-2 fill-purple-600 text-purple-600" />
          <span className="text-xs text-gray-500">China</span>
        </div>
        <div className="flex items-center gap-1">
          <Circle className="h-2 w-2 fill-blue-500 text-blue-500" />
          <span className="text-xs text-gray-500">UK</span>
        </div>
        <div className="flex items-center gap-1">
          <Circle className="h-2 w-2 fill-teal-500 text-teal-500" />
          <span className="text-xs text-gray-500">USA</span>
        </div>
        <div className="flex items-center gap-1">
          <Circle className="h-2 w-2 fill-red-500 text-red-500" />
          <span className="text-xs text-gray-500">Canada</span>
        </div>
        <div className="flex items-center gap-1">
          <Circle className="h-2 w-2 fill-gray-400 text-gray-400" />
          <span className="text-xs text-gray-500">Other</span>
        </div>
      </div>
    </div>
  )
}
