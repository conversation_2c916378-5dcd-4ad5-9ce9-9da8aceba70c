'use client'
import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
type Props = {
    p_email:string
    prices:{
      monthly:number,
      yearly:number
    }
}
export default function PaymentPage({p_email,prices}:Props) {
  const [email, setEmail] = useState(p_email);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [subscriptionPlan, setSubscriptionPlan] = useState<'monthly' | 'yearly'>('monthly'); // default monthly
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const searchParams = useSearchParams();

  useEffect(() => {
    // Check for messages from URL parameters
    const urlMessage = searchParams.get('message');
    if (urlMessage) {
      setMessage(urlMessage);
      // Clear message after 5 seconds
      setTimeout(() => setMessage(''), 5000);
    }
  }, [searchParams]);

  // Prices fixed in code
  

  const amount = prices[subscriptionPlan];

  const handlePayment = async () => {
    // Validate required fields
    if (!email) {
      alert('Please enter your email address');
      return;
    }

    setLoading(true);
    try {
      const res = await fetch('/api/payment', {
        method: 'POST',
        body: JSON.stringify({
          email,
          subscriptionPlan,
          amount,
          phoneNumber,
          customerName
        }),
        headers: { 'Content-Type': 'application/json' },
      });

      const data = await res.json();
      if (data.success) {
        if (data.paymentUrl) {
          // Redirect to Flutterwave payment page
          window.location.href = data.paymentUrl;
        } else {
          // Fallback to success page
          window.location.href = `/payment/success?token=${data.token}&email=${email}&plan=${subscriptionPlan}`;
        }
      } else {
        alert(data.error || 'Payment failed');
      }
    } catch (error) {
      alert('Payment processing failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-green-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">AH</span>
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                Azure Hotel
              </h1>
            </div>
            <div className="text-sm text-gray-500">
              Secure Payment
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Choose Your <span className="bg-green-600  bg-clip-text text-transparent">Subscription Plan</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Unlock premium features and take your hotel management to the next level
          </p>
        </div>

        {/* Display message if present */}
        {message && (
          <div className="mb-8 max-w-2xl mx-auto">
            <div className="bg-amber-50 border border-amber-200 rounded-xl p-4 flex items-start space-x-3">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-amber-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <p className="text-amber-800 text-sm font-medium">{message}</p>
            </div>
          </div>
        )}

        <div className="grid lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {/* Payment Form */}
          <div className="bg-white rounded-2xl shadow-xl border border-green-100 overflow-hidden">
            <div className="bg-gradient-to-r from-green-600 to-emerald-600 px-8 py-6">
              <h3 className="text-2xl font-bold text-white">Payment Details</h3>
              <p className="text-green-100 mt-1">Secure and encrypted</p>
            </div>

            <div className="p-8 space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                    disabled
                  placeholder="Enter your email address"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                  value={email}
                  required
                />
              </div>

              

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  placeholder="e.g., +237 671 234 567"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                />
                <p className="text-xs text-gray-500 mt-1">Required for mobile money payments</p>
              </div>

              {/* Payment Methods Info */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                <h4 className="font-semibold text-green-800 mb-3 flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  Payment Methods Available
                </h4>
                <div className="grid grid-cols-2 gap-3 text-sm text-green-700">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Mobile Money
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Bank Cards
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Bank Transfer
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    USSD & QR Code
                  </div>
                </div>
                <div className="mt-3 pt-3 border-t border-green-200">
                  <p className="text-xs text-green-600 font-medium flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                    </svg>
                    Powered by Flutterwave - Secure & Fast Payments
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Plan Selection */}
          <div className="space-y-6">
            <div className="bg-white rounded-2xl shadow-xl border border-green-100 overflow-hidden">
              <div className="bg-green-400 px-8 py-6">
                <h3 className="text-2xl font-bold text-white">Select Your Plan</h3>
                <p className="text-emerald-100 mt-1">Choose what works best for you</p>
              </div>

              <div className="p-8">
                <div className="space-y-4">
                  {/* Monthly Plan */}
                  <div
                    className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      subscriptionPlan === 'monthly'
                        ? 'border-green-500 bg-green-50 shadow-lg'
                        : 'border-gray-200 hover:border-green-300 hover:shadow-md'
                    }`}
                    onClick={() => setSubscriptionPlan('monthly')}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-xl font-bold text-gray-900">Monthly Plan</h4>
                        <p className="text-gray-600 mt-1">Perfect for getting started</p>
                      </div>
                      <div className="text-right">
                        <div className="text-3xl font-bold text-green-600">{prices.monthly.toLocaleString()}</div>
                        <div className="text-sm text-gray-500">XAF/month</div>
                      </div>
                    </div>
                    {subscriptionPlan === 'monthly' && (
                      <div className="absolute top-4 right-4">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Yearly Plan */}
                  <div
                    className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      subscriptionPlan === 'yearly'
                        ? 'border-green-500 bg-green-50 shadow-lg'
                        : 'border-gray-200 hover:border-green-300 hover:shadow-md'
                    }`}
                    onClick={() => setSubscriptionPlan('yearly')}
                  >
                    <div className="absolute -top-3 left-6">
                      <span className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                        SAVE {(prices.monthly * 12 - prices.yearly).toLocaleString()} XAF
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-xl font-bold text-gray-900">Yearly Plan</h4>
                        <p className="text-gray-600 mt-1">Best value for long-term use</p>
                      </div>
                      <div className="text-right">
                        <div className="text-3xl font-bold text-green-600">{prices.yearly.toLocaleString()}</div>
                        <div className="text-sm text-gray-500">XAF/year</div>
                        <div className="text-xs text-green-600 font-medium">
                          Save {(prices.monthly * 12 - prices.yearly).toLocaleString()} XAF
                        </div>
                      </div>
                    </div>
                    {subscriptionPlan === 'yearly' && (
                      <div className="absolute top-4 right-4">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Features List */}
                <div className="mt-8 p-6 bg-gray-50 rounded-xl">
                  <h5 className="font-semibold text-gray-900 mb-4">What's included:</h5>
                  <div className="grid grid-cols-1 gap-3">
                    {[
                      'Full access to hotel management system',
                      'Real-time booking management',
                      'Customer relationship tools',
                      'Analytics and reporting',
                      'Email support',
                      'Regular updates and new features'
                    ].map((feature, index) => (
                      <div key={index} className="flex items-center text-sm text-gray-700">
                        <svg className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Button */}
            <button
              type="button"
              onClick={handlePayment}
              disabled={loading || !email}
              className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 px-8 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing Payment...
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                  Subscribe for {amount.toLocaleString()} XAF
                  {subscriptionPlan === 'yearly' && ` (Save ${(prices.monthly * 12 - prices.yearly).toLocaleString()} XAF)`}
                </div>
              )}
            </button>

            {/* Security Notice */}
            <div className="text-center">
              <p className="text-sm text-gray-500 flex items-center justify-center">
                <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                Your payment is secured with 256-bit SSL encryption
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
