"use client"

import { useEffect, useRef, useState, useCallback } from "react";
import * as tf from '@tensorflow/tfjs';
import '@tensorflow/tfjs-backend-webgl';
import * as cocossd from '@tensorflow-models/coco-ssd';
import TakeExam from "./TakeExam";
import clsx from "clsx";
import { toast } from "sonner";

// Violation types with severity levels
const VIOLATIONS = {
  MULTIPLE_FACES: { type: 'Multiple Faces Detected', severity: 'HIGH', points: 3 },
  NO_FACE: { type: 'No Face Detected', severity: 'HIGH', points: 2 },
  FACE_NOT_CENTERED: { type: 'Face Not Properly Centered', severity: 'MEDIUM', points: 1 },
  DEVICE: { type: 'Unauthorized Device Detected', severity: 'CRITICAL', points: 5 },
  SUSPICIOUS_OBJECT: { type: 'Suspicious Object Detected', severity: 'HIGH', points: 3 },
  NOISE: { type: 'Excessive Background Noise', severity: 'MEDIUM', points: 1 },
  TAB_SWITCH: { type: 'Tab Switch/Window Change', severity: 'CRITICAL', points: 4 },
  FULLSCREEN_EXIT: { type: 'Exited Fullscreen Mode', severity: 'HIGH', points: 3 },
  COPY_PASTE: { type: 'Copy/Paste Activity', severity: 'HIGH', points: 3 },
  RIGHT_CLICK: { type: 'Right Click Detected', severity: 'MEDIUM', points: 2 },
  KEYBOARD_SHORTCUTS: { type: 'Suspicious Keyboard Activity', severity: 'MEDIUM', points: 2 }
};

// Enhanced settings for university exam
const SETTINGS = {
  CHECK_INTERVAL_MS: 1000,
  NOISE_THRESHOLD: 45,
  MAX_VIOLATION_POINTS: 15,
  FACE_CONFIDENCE_THRESHOLD: 0.7,
  OBJECT_CONFIDENCE_THRESHOLD: 0.6,
  GRACE_PERIOD_MS: 30000, // 30 seconds grace period at start
  WARNING_COOLDOWN_MS: 3000 // Prevent spam warnings
};

const SUSPICIOUS_OBJECTS = [
  'cell phone', 'laptop', 'book', 'remote', 'mouse', 'keyboard', 
  'tablet', 'monitor', 'tv', 'computer', 'phone'
];

interface UniversityAIProctorProps {
  studentName: string;
  studentId: string;
  examId: string;
  examTitle?: string;
  examData:any
}

export default function UniversityAIProctor({ 
  studentName, 
  studentId, 
  examId, 
  examTitle = "University Exam",
  examData
}: UniversityAIProctorProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [violations, setViolations] = useState<Array<{type: string, time: string, severity: string}>>([]);
  const [examBlocked, setExamBlocked] = useState(false);
  const [violationPoints, setViolationPoints] = useState(0);
  const [examStarted, setExamStarted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [cameraStatus, setCameraStatus] = useState('Initializing...');
  const [lastWarningTime, setLastWarningTime] = useState(0);
  const [examDuration, setExamDuration] = useState(0);
  const [isModelLoaded, setIsModelLoaded] = useState(false);
  const [isSubmittingViolation, setIsSubmittingViolation] = useState(false);

  const [proctoringPaused, setProctoringPaused] = useState(false);
  const [pauseReason, setPauseReason] = useState<string>('');
  const [pauseStartTime, setPauseStartTime] = useState<number>(0);
  const [totalPausedTime, setTotalPausedTime] = useState<number>(0);
  const [isSubmittingExam, setIsSubmittingExam] = useState(false);

  const [proctoringActive, setProctoringActive] = useState(true);
  const [showAIContainer, setShowAIContainer] = useState(true);


  const modelsRef = useRef<{
    objectDetection?: cocossd.ObjectDetection;
    audioAnalyser?: AnalyserNode;
    audioContext?: AudioContext;
  }>({});

const pauseProctoring = useCallback((reason: string = 'Manual pause') => {
    if (!proctoringPaused) {
      setProctoringPaused(true);
      setPauseReason(reason);
      setPauseStartTime(Date.now());
      console.log(`Proctoring paused: ${reason}`);
    }
  }, [proctoringPaused]);

  // NEW: Stop proctoring completely
  const stopProctoring = useCallback((reason: string = 'Proctoring stopped') => {
    setProctoringActive(false);
    setProctoringPaused(false);
    setShowAIContainer(false);
    console.log(`Proctoring stopped: ${reason}`);
    
    // Stop all media streams
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    
    // Close audio context
    if (modelsRef.current.audioContext) {
      modelsRef.current.audioContext.close();
    }
  }, []);

  // NEW: Restart proctoring
  const restartProctoring = useCallback(async () => {
    try {
      setProctoringActive(true);
      setShowAIContainer(true);
      setCameraStatus('Restarting camera...');
      
      // Reinitialize camera
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          width: { ideal: 640 }, 
          height: { ideal: 480 },
          facingMode: 'user'
        }, 
        audio: true 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraStatus('Camera restarted');
      }

      // Reinitialize audio
      const audioContext = new AudioContext();
      const source = audioContext.createMediaStreamSource(stream);
      const analyser = audioContext.createAnalyser();
      source.connect(analyser);
      analyser.fftSize = 256;
      
      modelsRef.current.audioContext = audioContext;
      modelsRef.current.audioAnalyser = analyser;
      
      console.log('Proctoring restarted successfully');
    } catch (error) {
      console.error('Failed to restart proctoring:', error);
      setCameraStatus('Failed to restart camera');
    }
  }, []);

   // NEW: Resume proctoring function
  const resumeProctoring = useCallback((reason: string = 'Manual resume') => {
    if (proctoringPaused) {
      const pauseDuration = Date.now() - pauseStartTime;
      setTotalPausedTime(prev => prev + pauseDuration);
      setProctoringPaused(false);
      setPauseReason('');
      setPauseStartTime(0);
      console.log(`Proctoring resumed: ${reason}. Paused for ${Math.round(pauseDuration / 1000)}s`);
    }
  }, [proctoringPaused, pauseStartTime]);
  // Initialize TensorFlow and models
  useEffect(() => {
    const initializeModels = async () => {
      try {
        await tf.setBackend('webgl');
        await tf.ready();
        
        // Load object detection model
        modelsRef.current.objectDetection = await cocossd.load();
        setIsModelLoaded(true);
        setCameraStatus('Models loaded successfully');
      } catch (error) {
        console.error('Failed to initialize models:', error);
        setCameraStatus('Failed to load AI models');
      }
    };
    
    initializeModels();
  }, []);

  // Record violation with enhanced tracking
  // Record violation with enhanced tracking
  const recordViolation = useCallback((violationType: keyof typeof VIOLATIONS) => {
    // Don't record violations when proctoring is paused or stopped
    if (proctoringPaused || !proctoringActive) return;
    
    const now = Date.now();
    if (now - lastWarningTime < SETTINGS.WARNING_COOLDOWN_MS) return;
    
    const violation = VIOLATIONS[violationType];
    const timestamp = new Date().toLocaleTimeString();
    
    // Update violation counts
   
    
    setViolations(prev => [...prev, {
      type: violation.type,
      time: timestamp,
      severity: violation.severity
    }]);
    
    setViolationPoints(prev => {
      const newPoints = prev + violation.points;
      if (newPoints >= SETTINGS.MAX_VIOLATION_POINTS) {
        setExamBlocked(true);
        // Log critical violation using most common violation type
        const mostCommonViolationType = getMostCommonViolation();
      }
      return newPoints;
    });
    
    setLastWarningTime(now);
  }, [lastWarningTime, proctoringPaused, proctoringActive]);

  // Enhanced API call to send violation data to backend
  const logCriticalViolation = async (violationType: string, timestamp: string) => {

    if (isSubmittingViolation || !violationType || !examId || !studentId || proctoringPaused) return;
    const violationData = {
      studentName,
      studentId,
      examId,
      examTitle,
      violationType,
      timestamp,
      examDuration,
      totalViolations: violations.length + 1,
      violationPoints: violationPoints + VIOLATIONS[violationType as keyof typeof VIOLATIONS].points,
      allViolations: [...violations, { type: violationType, time: timestamp, severity: VIOLATIONS[violationType as keyof typeof VIOLATIONS].severity }],
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      blockedAt: new Date().toISOString()
    };
    
    setIsSubmittingViolation(true);
    
    try {
      const response = await fetch('/api/exam/violation-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(violationData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Violation reported successfully:', result);
      
      // Also store locally as backup
      localStorage.setItem(`exam_violation_${examId}_${studentId}`, JSON.stringify(violationData));
      
    } catch (error) {
      console.error('Failed to report violation to backend:', error);
      
      // Store locally if API fails
      const backupData = {
        ...violationData,
        apiError: error instanceof Error ? error.message : 'Unknown error',
        failedAt: new Date().toISOString()
      };
      localStorage.setItem(`exam_violation_backup_${examId}_${studentId}`, JSON.stringify(backupData));
      
      // You might want to show a message to the user or retry logic here
      alert('Network error occurred while reporting violation. Your exam session has been logged locally.');
    } finally {
      setIsSubmittingViolation(false);
    }
  };

  // Initialize camera and audio monitoring
  useEffect(() => {
    let stream: MediaStream;
    let detectionInterval: NodeJS.Timeout;

    const initializeMonitoring = async () => {
      try {
        // Request camera and microphone permissions
        stream = await navigator.mediaDevices.getUserMedia({ 
          video: { 
            width: { ideal: 640 }, 
            height: { ideal: 480 },
            facingMode: 'user'
          }, 
          audio: true 
        });
        
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          setCameraStatus('Camera active');
        }

        // Setup audio analysis
        const audioContext = new AudioContext();
        const source = audioContext.createMediaStreamSource(stream);
        const analyser = audioContext.createAnalyser();
        source.connect(analyser);
        analyser.fftSize = 256;
        
        modelsRef.current.audioContext = audioContext;
        modelsRef.current.audioAnalyser = analyser;

        // Start detection loop - now considers paused state and active state
        detectionInterval = setInterval(() => {
          if (examStarted && !examBlocked && !proctoringPaused && proctoringActive && isModelLoaded) {
            performDetections();
          }
        }, SETTINGS.CHECK_INTERVAL_MS);

      } catch (error) {
        console.error('Failed to initialize monitoring:', error);
        setCameraStatus('Camera access denied');
      }
    };

    if (isModelLoaded) {
      initializeMonitoring();
    }

    return () => {
      if (detectionInterval) clearInterval(detectionInterval);
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      if (modelsRef.current.audioContext) {
        modelsRef.current.audioContext.close();
      }
    };
  }, [isModelLoaded, examStarted, examBlocked, proctoringPaused, proctoringActive, recordViolation]);

  const getMostCommonViolation = () => {
    const violationCounts: Record<string, number> = {};
    violations.forEach(violation => {
      violationCounts[violation.type] = (violationCounts[violation.type] || 0) + 1;
    });
    return Object.entries(violationCounts).reduce((a, b) => b[1] > a[1] ? b : a)[0];
  }


  useEffect(() => {
    const log = async () => {
      if (examBlocked) {
        const mostCommonViolation = getMostCommonViolation();
        await logCriticalViolation(mostCommonViolation, new Date().toISOString());
      }
    }
    log();
  },[examBlocked])
  // Enhanced detection system
  const performDetections = async () => {
    toast.success(proctoringPaused)
    if( proctoringPaused || !isModelLoaded || !videoRef.current || !proctoringActive) return;
    if (!videoRef.current || !modelsRef.current.objectDetection) return;

    try {
      // Object detection
      const predictions = await modelsRef.current.objectDetection.detect(videoRef.current);
      
      // Check for unauthorized devices and objects
      const suspiciousObjects = predictions.filter(pred => 
        SUSPICIOUS_OBJECTS.includes(pred.class.toLowerCase()) && 
        pred.score > SETTINGS.OBJECT_CONFIDENCE_THRESHOLD
      );
      
      if (suspiciousObjects.length > 0) {
        const hasPhone = suspiciousObjects.some(obj => obj.class.toLowerCase().includes('phone'));
        const hasComputer = suspiciousObjects.some(obj => 
          ['laptop', 'computer', 'monitor', 'tablet'].includes(obj.class.toLowerCase())
        );
        
        if (hasPhone || hasComputer) {
          recordViolation('DEVICE');
        } else {
          recordViolation('SUSPICIOUS_OBJECT');
        }
      }

      // Face detection
      const faceDetections = predictions.filter(pred => pred.class === 'person');
      if (faceDetections.length === 0) {
        recordViolation('NO_FACE');
      } else if (faceDetections.length > 1) {
        recordViolation('MULTIPLE_FACES');
      }

      // Audio analysis
      if (modelsRef.current.audioAnalyser) {
        const dataArray = new Uint8Array(modelsRef.current.audioAnalyser.frequencyBinCount);
        modelsRef.current.audioAnalyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
        
        if (average > SETTINGS.NOISE_THRESHOLD) {
          recordViolation('NOISE');
        }
      }

    } catch (error) {
      console.error('Detection error:', error);
    }
  };

  // Enhanced security event listeners
    useEffect(() => {
      if (!examStarted || proctoringPaused || !proctoringActive) return;
    const handleVisibilityChange = () => {
      if (document.hidden && examStarted && !proctoringPaused && proctoringActive) {
        recordViolation('TAB_SWITCH');
      }
    };

    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!document.fullscreenElement;
      setIsFullscreen(isCurrentlyFullscreen);
      
      if (!isCurrentlyFullscreen && examStarted && !proctoringPaused && proctoringActive) {
        recordViolation('FULLSCREEN_EXIT');
      }
    };

    const handleContextMenu = (e: MouseEvent) => {
      if (examStarted && !proctoringPaused && proctoringActive) {
        e.preventDefault();
        recordViolation('RIGHT_CLICK');
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (!examStarted || proctoringPaused || !proctoringActive) return;
      
      // Detect common cheating shortcuts
      const suspiciousKeys = [
        'F12', 'F5', 'F11', 'Alt', 'Tab', 'Meta', 'Control'
      ];
      
      if (suspiciousKeys.includes(e.key) || 
          (e.ctrlKey && ['c', 'v', 'a', 'f'].includes(e.key.toLowerCase())) ||
          (e.metaKey && ['c', 'v', 'a', 'f'].includes(e.key.toLowerCase()))) {
        e.preventDefault();
        recordViolation('KEYBOARD_SHORTCUTS');
      }
    };

    const handleCopyPaste = (e: ClipboardEvent) => {
      if (examStarted && !proctoringPaused && proctoringActive) {
        e.preventDefault();
        recordViolation('COPY_PASTE');
      }
    };

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('copy', handleCopyPaste);
    document.addEventListener('paste', handleCopyPaste);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('copy', handleCopyPaste);
      document.removeEventListener('paste', handleCopyPaste);
    };
  }, [examStarted, proctoringPaused, proctoringActive, recordViolation]);

  // Exam timer - doesn't count when paused or stopped
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (examStarted && !examBlocked && !proctoringPaused && proctoringActive) {
      timer = setInterval(() => {
        setExamDuration(prev => prev + 1);
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [examStarted, examBlocked, proctoringPaused, proctoringActive]);

  // Exam timer
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (examStarted && !examBlocked) {
      timer = setInterval(() => {
        setExamDuration(prev => prev + 1);
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [examStarted, examBlocked]);

  // Start exam with enhanced validation
  const startExam = async () => {
    if (!isModelLoaded) {
      alert('AI models are still loading. Please wait.');
      return;
    }

    try {
      await document.documentElement.requestFullscreen();
      setExamStarted(true);
      setIsFullscreen(true);
    } catch (error) {
      alert('Fullscreen mode is required for the exam');
    }
  };

  // Format time display
  const formatTime = (seconds: number) => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get violation color based on severity
  const getViolationColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'text-red-700 bg-red-100';
      case 'HIGH': return 'text-orange-700 bg-orange-100';
      case 'MEDIUM': return 'text-yellow-700 bg-yellow-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  if (examBlocked) {
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-700 mb-4">Exam Terminated</h1>
          <p className="text-gray-700 mb-6">
            Your exam has been terminated due to multiple violations. 
            Total violation points: <span className="font-bold text-red-600">{violationPoints}</span>
          </p>
          <p className="text-sm text-gray-600 mb-4">
            Exam duration: {formatTime(examDuration)}
          </p>
          <p className="text-sm text-gray-500">
            Please contact your instructor or IT support for assistance.
          </p>
        </div>
      </div>
    );
  }

  if (!examStarted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-lg w-full">
          <h1 className="text-3xl font-bold text-center mb-8 text-blue-700">
            {examTitle}
          </h1>
          
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <h3 className="font-semibold text-blue-800 mb-2">Student Information:</h3>
              <div className="text-sm text-blue-700 space-y-1">
                <div><strong>Name:</strong> {studentName}</div>
                <div><strong>ID:</strong> {studentId}</div>
                <div><strong>Exam ID:</strong> {examId}</div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <h3 className="font-semibold text-yellow-800 mb-2">Exam Rules:</h3>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Keep your face visible and centered</li>
                <li>• No unauthorized devices (phones, tablets, etc.)</li>
                <li>• Stay in fullscreen mode</li>
                <li>• No tab switching or window changes</li>
                <li>• Minimize background noise</li>
                <li>• No copy/paste or right-click actions</li>
              </ul>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                Status: <span className={`font-semibold ${isModelLoaded ? 'text-green-600' : 'text-orange-600'}`}>
                  {cameraStatus}
                </span>
              </p>
              
              <button
                onClick={startExam}
                disabled={!isModelLoaded}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-3 px-6 rounded-lg transition duration-200"
              >
                {isModelLoaded ? 'Start Exam' : 'Loading...'}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx("min-h-screen bg-gray-100 ",{
        'flex':proctoringActive && showAIContainer,
    })}>
      {/* Main exam area */}
      <div className="flex-1 p-6">
        <div className="bg-white rounded-lg shadow-sm p-6 h-full">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800">University Exam</h1>
            <div className="text-right">
              <div className="text-lg font-mono text-blue-600">{formatTime(examDuration)}</div>
              <div className="text-sm text-gray-500">Exam Time</div>
            </div>
          </div>
          
          {/* Exam content area */}
              <div className="h-full min-h-full overflow-y-auto max-h-full">
                <TakeExam Data={examData} studentId={studentId} sessionId={examId} stopProctoring={stopProctoring} pauseProctoring={pauseProctoring} resumeProctoring={resumeProctoring} />
              </div>
           
        </div>
      </div>

      {/* Proctoring panel */}
      {
        proctoringActive && showAIContainer && (
          <div className="w-80 bg-white shadow-lg p-6 overflow-y-auto">
        <div className="mb-6">
          <h2 className="text-lg font-bold text-gray-800 mb-2">AI Proctoring</h2>
          <div className="text-sm text-gray-600">
            Student: {studentName} ({studentId})
          </div>
        </div>
        <div className={`p-4 text-white ${proctoringPaused ? 'bg-yellow-600' : 'bg-green-600'}`}>
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-bold">AI Proctoring Active</h3>
            <p className="text-sm">
              {proctoringPaused ? `PAUSED: ${pauseReason}` : 'Monitoring in progress'}
            </p>
          </div>
          <div className="text-right">
            <p className="font-mono">{formatTime(examDuration)}</p>
            <p className="text-sm">Violations: {violationPoints}/{SETTINGS.MAX_VIOLATION_POINTS}</p>
            {proctoringPaused && (
              <p className="text-xs">Paused Time: {Math.floor(totalPausedTime / 1000)}s</p>
            )}
          </div>
        </div>
      </div>

        {/* Camera feed */}
        <div className="mb-6">
          <video
            ref={videoRef}
            autoPlay
            muted
            playsInline
            className="w-full aspect-video rounded-lg bg-black"
          />
          <canvas ref={canvasRef} className="hidden" />
        </div>

        {/* Violation status */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Violation Points</span>
            <span className={`font-bold ${violationPoints > 10 ? 'text-red-600' : violationPoints > 5 ? 'text-orange-600' : 'text-green-600'}`}>
              {violationPoints}/{SETTINGS.MAX_VIOLATION_POINTS}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${violationPoints > 10 ? 'bg-red-500' : violationPoints > 5 ? 'bg-orange-500' : 'bg-green-500'}`}
              style={{ width: `${(violationPoints / SETTINGS.MAX_VIOLATION_POINTS) * 100}%` }}
            />
          </div>
        </div>

        {/* Violations log */}
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-2">
            Violations ({violations.length})
          </h3>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {violations.slice(-10).map((violation, index) => (
              <div
                key={index}
                className={`text-xs p-2 rounded ${getViolationColor(violation.severity)}`}
              >
                <div className="font-semibold">{violation.type}</div>
                <div className="opacity-75">{violation.time}</div>
              </div>
            ))}
            {violations.length === 0 && (
              <div className="text-xs text-gray-500 text-center py-4">
                No violations detected
              </div>
            )}
          </div>
        </div>

        {/* Status indicators */}
        <div className="mt-6 space-y-2 text-xs">
          <div className="flex justify-between">
            <span>Camera Status:</span>
            <span className="text-green-600">Active</span>
          </div>
          <div className="flex justify-between">
            <span>Fullscreen:</span>
            <span className={isFullscreen ? 'text-green-600' : 'text-red-600'}>
              {isFullscreen ? 'Yes' : 'No'}
            </span>
          </div>
          <div className="flex justify-between">
            <span>AI Models:</span>
            <span className="text-green-600">Loaded</span>
          </div>
        </div>
      </div>)
      }
    </div>
  );
}