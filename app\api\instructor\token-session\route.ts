import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { createInstructorSession } from '@/app/lib/instructor-session';
import { createInstructorTokenSession } from '@/app/lib/instructor-token-session';
import { Code } from 'lucide-react';

// POST: Validate instructor token and create session
export async function POST(req: NextRequest) {
  const { token } = await req.json();
  if (!token || token.length < 6) {
    return NextResponse.json({ success: false, error: 'Invalid token' }, { status: 400 });
  }
  // Find instructor token
  const instructorToken = await prisma.instructorToken.findFirst({
    where: { code: token, status: 'active' },
    include: { univ: true },
  });
  if (!instructorToken) {
    return NextResponse.json({ success: false, error: 'Token not found or inactive' }, { status: 404 });
  }
  // Create a temporary instructor session (no instructor yet, just token info)
  await createInstructorTokenSession({
    univId: instructorToken.univId,
    token,
    univName:instructorToken.univ.name
  });
  return NextResponse.json({ success: true, univId: instructorToken.univId, univName: instructorToken.univ.name });
}
