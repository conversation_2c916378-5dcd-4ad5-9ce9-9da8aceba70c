# SmartOnline - University Management System

A comprehensive university management platform built with Next.js 15, Prisma, and Sanity CMS. The system provides features for university administration, student management, payment processing, and subscription handling.

## 🌟 Features

### Administrative Features
- **University Management**
  - Create and manage universities
  - Department and course management
  - Instructor token generation
  - Student enrollment tracking
  - Logo and branding customization

### Student Features
- **Student Portal**
  - Personal dashboard
  - Course enrollment
  - Profile management
  - Exam scheduling
  - Result tracking
  - Question bank access

### Payment & Subscription
- **Multiple Payment Gateways**
  - Flutterwave integration
  - MTN Mobile Money
  - Orange Money
  - PayPal support
- **Subscription Management**
  - Multiple plan options
  - Automated renewal reminders
  - Usage tracking
  - Coupon system

### Security & Authentication
- JWT-based authentication
- Role-based access control
- Email verification
- Secure password handling

## 🛠 Tech Stack

- **Frontend**: Next.js 15.0.1
- **Styling**: TailwindCSS
- **State Management**: Zustand
- **Database**: PostgreSQL with Prisma ORM
- **CMS**: Sanity
- **Authentication**: JWT with jose
- **Email**: Nodemailer
- **Charts**: Chart.js with react-chartjs-2
- **UI Components**: 
  - Radix UI
  - Heroicons
  - Lucide React
- **Development**:
  - TypeScript
  - ESLint
  - PostCSS

## 📋 Prerequisites

- Node.js >= 18
- PostgreSQL database
- Sanity account (for CMS)
- Payment gateway accounts (for production)

## 🚀 Getting Started

1. **Clone the repository**
```bash
git clone <repository-url>
cd smartonline
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Setup**
Create a `.env` file with:
```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/smartonline"

# Authentication
JWT_SECRET="your-secure-jwt-secret"

# Email
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-smtp-username"
SMTP_PASS="your-smtp-password"

# Sanity
NEXT_PUBLIC_SANITY_PROJECT_ID="your-sanity-project-id"
NEXT_PUBLIC_SANITY_DATASET="production"

# Payment Gateways
FLUTTERWAVE_PUBLIC_KEY="your-flutterwave-public-key"
FLUTTERWAVE_SECRET_KEY="your-flutterwave-secret-key"
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"
```

4. **Database Setup**
```bash
# Generate Prisma client
npx prisma generate

# Push database schema (for development)
npx prisma db push

# Or run migrations (for production)
npx prisma migrate deploy

# Optional: Seed database with sample data
npx prisma db seed
```

5. **Start Development Server**
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## 🔧 Additional Setup Commands

### Database Management
```bash
# View database in Prisma Studio
npm run db:studio

# Reset database (development only)
npm run db:reset

# Create new migration
npm run db:migrate

# Generate Prisma client
npm run db:generate
```

### Code Quality
```bash
# Run TypeScript type checking
npm run type-check

# Run linter
npm run lint

# Fix linting issues
npm run lint:fix

# Run tests
npm run test

# Run tests in watch mode
npm run test:watch

# Generate test coverage report
npm run test:coverage
```

### Production Build
```bash
# Build for production
npm run build

# Start production server
npm start

# Clean build cache
npm run clean
```

## 📁 Project Structure

```
smartonline/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin dashboard
│   ├── api/               # API routes
│   ├── component/         # Shared components
│   ├── lib/              # Utility functions
│   ├── payment/          # Payment processing
│   └── univ/             # University-specific pages
├── lib/                   # Core business logic
├── prisma/               # Database schema
├── public/              # Static assets
├── sanity/             # CMS configuration
└── utils/              # Helper utilities
```

## 🔐 Authentication System

The system implements a multi-tenant authentication system:

1. **Admin Authentication**
   - JWT-based authentication
   - Session management
   - Role-based access control

2. **Student Authentication**
   - University-specific login
   - Course access control
   - Token-based verification

3. **Instructor Authentication**
   - Department-specific access
   - Course management permissions
   - Token generation capabilities

## 💳 Payment Integration

### Supported Payment Methods
- Flutterwave (Cards, Bank transfers)
- Mobile Money (MTN, Orange)
- PayPal (International payments)

### Subscription Plans
- Multiple tier support
- Flexible billing cycles
- Feature-based access control
- Usage limitations

## 📊 Database Schema

Key models include:
- University
- Department
- Course
- Student
- Instructor
- Admin
- Payment
- Subscription
- Token

Refer to `prisma/schema.prisma` for complete schema details.

## 🚀 Deployment

### Docker Deployment

#### Using Docker Compose (Recommended for local development)
```bash
# Start all services (app, database, redis)
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

#### Manual Docker Build
```bash
# Build Docker image
docker build -t smartonline .

# Run container
docker run -p 3000:3000 \
  -e DATABASE_URL="your-database-url" \
  -e JWT_SECRET="your-jwt-secret" \
  smartonline
```

### Production Build
```bash
npm run build
npm start
```

### Deployment Platforms
- **Vercel** (Recommended for Next.js)
- **Docker** (Any container platform)
- **AWS** (EC2, ECS, Lambda)
- **DigitalOcean** (App Platform, Droplets)
- **Railway** (Simple deployment)
- **Render** (Full-stack hosting)

### Environment Variables for Production
Ensure these are set in your production environment:
```env
NODE_ENV=production
DATABASE_URL=your-production-database-url
JWT_SECRET=your-secure-jwt-secret
FLUTTERWAVE_PUBLIC_KEY=your-live-public-key
FLUTTERWAVE_SECRET_KEY=your-live-secret-key
EMAIL_USER=your-production-email
EMAIL_PASS=your-production-email-password
NEXT_PUBLIC_BASE_URL=https://your-domain.com
```

## 🔍 Monitoring

### Health Check Endpoints

#### System Status
```bash
# Check overall system health
curl http://localhost:3000/api/status

# Response includes:
# - Database connectivity
# - Email service configuration
# - Payment gateway status
# - System statistics
```

#### Development Debug Endpoints
```bash
# Environment variables check
curl http://localhost:3000/api/debug/env

# Email service diagnostics
curl http://localhost:3000/api/debug/email

# Network connectivity test
curl http://localhost:3000/api/debug/network
```

### Production Monitoring

#### System Monitoring Dashboard
Access the comprehensive monitoring dashboard at:
```
http://localhost:3000/admin/monitoring
```

Features:
- Real-time system health status
- Service-specific monitoring (database, email, payment gateway, external dependencies)
- Response time tracking
- Failure count monitoring
- Alert configuration status
- Auto-refresh every 30 seconds

#### Monitoring API Endpoints
```bash
# Get comprehensive monitoring data
curl http://localhost:3000/api/monitoring

# Get basic health status (legacy endpoint)
curl http://localhost:3000/api/status

# Trigger health check
curl -X POST http://localhost:3000/api/monitoring \
  -H "Content-Type: application/json" \
  -d '{"action": "health-check"}'
```

#### Alert Configuration
Configure monitoring alerts via environment variables:
```bash
# Enable alerts
MONITORING_ALERTS_ENABLED=true

# Email recipients (comma-separated)
MONITORING_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# Webhook URL for Slack/Discord notifications
MONITORING_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url

# Thresholds
MONITORING_RESPONSE_TIME_THRESHOLD=5000  # milliseconds
MONITORING_CONSECUTIVE_FAILURES_THRESHOLD=3
```

#### Nginx Health Check
The included `nginx.conf` provides a health check endpoint:
```
GET /health -> proxies to /api/status
```

#### Docker Health Check
```dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/status || exit 1
```

### Logging
- **Application logs**: Console output with environment-based filtering
- **Payment transaction logs**: Detailed payment processing logs
- **Error tracking**: Structured error logging with context
- **Activity logs**: User actions and system events

### Metrics to Monitor
- **Database**: Connection pool, query performance
- **API**: Response times, error rates
- **Payments**: Success rates, failed transactions
- **Email**: Delivery rates, bounce rates
- **System**: Memory usage, CPU utilization

## 🧪 Testing

```bash
# Run tests
npm test

# Lint code
npm run lint
```

## 📝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Support

For support:
- Create an issue
- Contact <EMAIL>
- Visit our documentation site

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Prisma team for the robust ORM
- Sanity team for the headless CMS
- All contributors to this project
