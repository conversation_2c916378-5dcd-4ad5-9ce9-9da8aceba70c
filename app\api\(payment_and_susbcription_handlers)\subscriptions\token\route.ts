import { createToken } from "@/app/lib/token"
import { NextResponse } from "next/server"

export async function POST(request:Request) {
    const {subscriptionId,user}:{subscriptionId:string,user:{email:string,id:string}} = await request.json()

    try{
        await createToken({
        adminId:user.id,
        subscriptionId:subscriptionId,
        email:user.email
    })

    return NextResponse.json({success:true},{status:200})
    }catch(err){
        console.log(err)
        return NextResponse.json({succes:false},{status:500})
    }
}