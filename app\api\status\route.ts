import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { EmailService } from '@/lib/email-service';
import { MonitoringService } from '@/lib/monitoring-service';

const prisma = new PrismaClient();
const monitoringService = MonitoringService.getInstance();

export async function GET() {
  try {
    // Use the enhanced monitoring service for comprehensive health check
    const systemHealth = await monitoringService.checkSystemHealth();

    // Convert to legacy format for backward compatibility
    const status = {
      timestamp: systemHealth.timestamp,
      status: systemHealth.overall,
      services: {
        database: systemHealth.services.find(s => s.name === 'database')?.status || 'unknown',
        email: systemHealth.services.find(s => s.name === 'email')?.status || 'unknown',
        paymentGateways: {
          flutterwave: systemHealth.services.find(s => s.name === 'payment_gateway')?.status || 'unknown',
        },
      },
      statistics: {
        totalUsers: 0,
        totalSubscriptions: 0,
        activeSubscriptions: 0,
        totalPayments: 0,
        completedPayments: 0,
      },
      // Enhanced monitoring data
      monitoring: {
        uptime: systemHealth.uptime,
        version: systemHealth.version,
        detailedServices: systemHealth.services,
      },
    };

    // The monitoring service has already checked all services
    // Just get statistics if database is healthy

    // Get statistics (only if database is healthy)
    if (status.services.database === 'healthy') {
      try {
        // Count payments (using current schema)
        status.statistics.totalPayments = await prisma.payment.count();
        status.statistics.completedPayments = await prisma.payment.count({
          where: { subscription: 'completed' },
        });

        // Count unique users by email
        const uniqueEmails = await prisma.payment.findMany({
          select: { email: true },
          distinct: ['email'],
        });
        status.statistics.totalUsers = uniqueEmails.length;

        // Count active subscriptions (simplified)
        status.statistics.activeSubscriptions = await prisma.payment.count({
          where: { subscription: 'completed' },
        });
        status.statistics.totalSubscriptions = status.statistics.activeSubscriptions;
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Failed to get statistics:', error);
        }
      }
    }

    return NextResponse.json(status);
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Status check failed:', error);
    }
    return NextResponse.json(
      {
        timestamp: new Date().toISOString(),
        status: 'unhealthy',
        error: 'Status check failed',
        services: {
          database: 'unknown',
          email: 'unknown',
          paymentGateways: {
            flutterwave: 'unknown',
          },
        },
      },
      { status: 500 }
    );
  }
}
