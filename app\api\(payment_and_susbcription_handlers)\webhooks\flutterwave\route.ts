import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { FlutterwaveService } from '@/lib/payment-gateways/flutterwave';
import { EmailService } from '@/lib/email-service';
import { ActivityService } from '@/lib/activity-service';

const prisma = new PrismaClient();
const flutterwaveService = new FlutterwaveService();

export async function POST(req: Request) {
  try {
    const body = await req.text();
    const signature = req.headers.get('verif-hash');

    // Verify webhook signature
    if (!signature || !flutterwaveService.verifyWebhookSignature(body, signature)) {
      console.log('Invalid webhook signature');
      return NextResponse.json({ success: false, error: 'Invalid signature' }, { status: 401 });
    }

    const webhookData = JSON.parse(body);
    console.log('Flutterwave Webhook received:', webhookData);

    const { event, data } = webhookData;

    // Handle different payment events
    if (event === 'charge.completed') {
      const { tx_ref, status, amount, currency, customer, flw_ref } = data;

      // Find payment by transaction reference
      const payment = await prisma.payment.findFirst({
        where: { token: tx_ref },
      });

      if (!payment) {
        console.log('Payment not found for tx_ref:', tx_ref);
        return NextResponse.json({ success: false, error: 'Payment not found' }, { status: 404 });
      }

      // Verify payment with Flutterwave API
      try {
        const verification = await flutterwaveService.verifyPaymentByTxRef(tx_ref);
        
        if (verification.status === 'success' && verification.data?.status === 'successful') {
          // Check if payment was already completed to prevent duplicate processing
          if (payment.subscription === 'completed') {
            console.log('Payment already processed:', tx_ref);
            return NextResponse.json({ success: true, message: 'Payment already processed' });
          }

          // Update payment status
          await prisma.payment.update({
            where: { id: payment.id },
            data: {
              subscription: 'completed',
            },
          });

          console.log('Payment verified and updated:', tx_ref);

          // Log payment completion activity
          await ActivityService.logPaymentCompleted(
            payment.id,
            payment.userId || '',
            verification.data.amount,
            verification.data.currency || 'XAF'
          );

          // Send receipt email only for newly completed payments
          const emailResult = await EmailService.sendPaymentConfirmationEmail(
            payment.email,
            payment.token,
            payment.amount,
            verification.data.currency || 'XAF',
            'Subscription Plan' // Plan name
          );

          if (emailResult.success) {
            console.log('Receipt email sent to:', payment.email);
          } else {
            console.error('Failed to send receipt email:', emailResult.message);
            // Don't fail the webhook if email fails
          }

          // TODO: Create subscription once Prisma client is updated
          // For now, we'll just log the successful payment
          console.log('Payment completed successfully:', {
            txRef: tx_ref,
            amount: verification.data.amount,
            currency: verification.data.currency,
            customer: verification.data.customer.email,
          });

        } else {
          console.log('Payment verification failed:', verification);

          // Update payment as failed
          await prisma.payment.update({
            where: { id: payment.id },
            data: {
              subscription: 'failed',
            },
          });

          // Log payment failure activity
          await ActivityService.logActivity({
            type: ActivityService.ACTIVITY_TYPES.PAYMENT_FAILED,
            action: `Payment failed: ${payment.amount} ${payment.currency}`,
            userId: payment.userId || undefined,
            targetId: payment.id,
            targetType: 'payment',
            metadata: {
              amount: payment.amount,
              currency: payment.currency,
              reason: verification.message || 'Payment verification failed'
            },
          });
        }
      } catch (verificationError) {
        console.error('Payment verification error:', verificationError);
        
        // Update payment as failed
        await prisma.payment.update({
          where: { id: payment.id },
          data: {
            subscription: 'failed',
          },
        });
      }
    }

    // Handle charge.failed or cancelled events
    if (event === 'charge.failed' || event === 'charge.cancelled') {
      const { tx_ref } = data;

      // Find payment by transaction reference
      const payment = await prisma.payment.findFirst({
        where: { token: tx_ref },
      });

      if (payment) {
        // Update payment status
        await prisma.payment.update({
          where: { id: payment.id },
          data: {
            subscription: event === 'charge.cancelled' ? 'cancelled' : 'failed',
          },
        });

        console.log(`Payment ${event} for tx_ref:`, tx_ref);
      }
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Flutterwave webhook processing failed:', error);
    return NextResponse.json(
      { success: false, error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}
