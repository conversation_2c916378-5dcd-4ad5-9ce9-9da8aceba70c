import { PrismaClient } from '@prisma/client';
import { EmailService } from './email-service';

const prisma = new PrismaClient();

export interface ServiceStatus {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded' | 'not_configured';
  lastChecked: string;
  responseTime?: number;
  error?: string;
  details?: any;
}

export interface SystemHealth {
  overall: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  services: ServiceStatus[];
  uptime: number;
  version: string;
}

export interface AlertConfig {
  enabled: boolean;
  emailRecipients: string[];
  webhookUrl?: string;
  thresholds: {
    responseTime: number; // ms
    errorRate: number; // percentage
    consecutiveFailures: number;
  };
}

export class MonitoringService {
  private static instance: MonitoringService;
  private alertConfig: AlertConfig;
  private serviceFailures: Map<string, number> = new Map();
  private startTime: number = Date.now();

  constructor() {
    this.alertConfig = {
      enabled: process.env.MONITORING_ALERTS_ENABLED === 'true',
      emailRecipients: process.env.MONITORING_EMAIL_RECIPIENTS?.split(',') || [],
      webhookUrl: process.env.MONITORING_WEBHOOK_URL,
      thresholds: {
        responseTime: parseInt(process.env.MONITORING_RESPONSE_TIME_THRESHOLD || '5000'),
        errorRate: parseInt(process.env.MONITORING_ERROR_RATE_THRESHOLD || '10'),
        consecutiveFailures: parseInt(process.env.MONITORING_CONSECUTIVE_FAILURES_THRESHOLD || '3'),
      },
    };
  }

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  async checkSystemHealth(): Promise<SystemHealth> {
    const services: ServiceStatus[] = [];
    
    // Check database
    services.push(await this.checkDatabase());
    
    // Check email service
    services.push(await this.checkEmailService());
    
    // Check payment gateway
    services.push(await this.checkPaymentGateway());
    
    // Check external dependencies
    services.push(await this.checkExternalDependencies());

    // Determine overall health
    const unhealthyServices = services.filter(s => s.status === 'unhealthy');
    const degradedServices = services.filter(s => s.status === 'degraded');
    
    let overall: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
    if (unhealthyServices.length > 0) {
      overall = 'unhealthy';
    } else if (degradedServices.length > 0) {
      overall = 'degraded';
    }

    const health: SystemHealth = {
      overall,
      timestamp: new Date().toISOString(),
      services,
      uptime: Date.now() - this.startTime,
      version: process.env.npm_package_version || '1.0.0',
    };

    // Check for alerts
    await this.processAlerts(health);

    return health;
  }

  private async checkDatabase(): Promise<ServiceStatus> {
    const startTime = Date.now();
    try {
      await prisma.$queryRaw`SELECT 1`;
      const responseTime = Date.now() - startTime;
      
      // Get database statistics
      const userCount = await prisma.user.count().catch(() => 0);
      const paymentCount = await prisma.payment.count().catch(() => 0);
      
      this.resetFailureCount('database');
      
      return {
        name: 'database',
        status: responseTime > this.alertConfig.thresholds.responseTime ? 'degraded' : 'healthy',
        lastChecked: new Date().toISOString(),
        responseTime,
        details: {
          userCount,
          paymentCount,
          connectionPool: 'active',
        },
      };
    } catch (error: any) {
      this.incrementFailureCount('database');
      return {
        name: 'database',
        status: 'unhealthy',
        lastChecked: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private async checkEmailService(): Promise<ServiceStatus> {
    const startTime = Date.now();
    try {
      if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
        return {
          name: 'email',
          status: 'not_configured',
          lastChecked: new Date().toISOString(),
          error: 'Email credentials not configured',
        };
      }

      // Get email statistics
      const stats = await EmailService.getEmailStats();
      const responseTime = Date.now() - startTime;
      
      this.resetFailureCount('email');
      
      return {
        name: 'email',
        status: 'healthy',
        lastChecked: new Date().toISOString(),
        responseTime,
        details: stats,
      };
    } catch (error: any) {
      this.incrementFailureCount('email');
      return {
        name: 'email',
        status: 'unhealthy',
        lastChecked: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private async checkPaymentGateway(): Promise<ServiceStatus> {
    const startTime = Date.now();
    try {
      if (!process.env.FLUTTERWAVE_PUBLIC_KEY || !process.env.FLUTTERWAVE_SECRET_KEY) {
        return {
          name: 'payment_gateway',
          status: 'not_configured',
          lastChecked: new Date().toISOString(),
          error: 'Flutterwave credentials not configured',
        };
      }

      // Test Flutterwave API connectivity
      const response = await fetch('https://api.flutterwave.com/v3/banks/NG', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.FLUTTERWAVE_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        this.resetFailureCount('payment_gateway');
        return {
          name: 'payment_gateway',
          status: responseTime > this.alertConfig.thresholds.responseTime ? 'degraded' : 'healthy',
          lastChecked: new Date().toISOString(),
          responseTime,
          details: {
            provider: 'flutterwave',
            apiStatus: 'operational',
          },
        };
      } else {
        this.incrementFailureCount('payment_gateway');
        return {
          name: 'payment_gateway',
          status: 'unhealthy',
          lastChecked: new Date().toISOString(),
          responseTime,
          error: `API returned ${response.status}`,
        };
      }
    } catch (error: any) {
      this.incrementFailureCount('payment_gateway');
      return {
        name: 'payment_gateway',
        status: 'unhealthy',
        lastChecked: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private async checkExternalDependencies(): Promise<ServiceStatus> {
    const startTime = Date.now();
    try {
      // Check internet connectivity and DNS resolution
      const response = await fetch('https://www.google.com', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000),
      });

      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        this.resetFailureCount('external_deps');
        return {
          name: 'external_dependencies',
          status: 'healthy',
          lastChecked: new Date().toISOString(),
          responseTime,
          details: {
            internetConnectivity: 'available',
            dnsResolution: 'working',
          },
        };
      } else {
        this.incrementFailureCount('external_deps');
        return {
          name: 'external_dependencies',
          status: 'degraded',
          lastChecked: new Date().toISOString(),
          responseTime,
          error: 'Limited connectivity',
        };
      }
    } catch (error: any) {
      this.incrementFailureCount('external_deps');
      return {
        name: 'external_dependencies',
        status: 'unhealthy',
        lastChecked: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private incrementFailureCount(serviceName: string): void {
    const current = this.serviceFailures.get(serviceName) || 0;
    this.serviceFailures.set(serviceName, current + 1);
  }

  private resetFailureCount(serviceName: string): void {
    this.serviceFailures.set(serviceName, 0);
  }

  private async processAlerts(health: SystemHealth): Promise<void> {
    if (!this.alertConfig.enabled) {
      return;
    }

    const criticalServices = health.services.filter(s => s.status === 'unhealthy');
    const degradedServices = health.services.filter(s => s.status === 'degraded');

    // Check for consecutive failures
    for (const service of criticalServices) {
      const failures = this.serviceFailures.get(service.name) || 0;
      if (failures >= this.alertConfig.thresholds.consecutiveFailures) {
        await this.sendAlert('critical', service, failures);
      }
    }

    // Check for degraded services
    for (const service of degradedServices) {
      if (service.responseTime && service.responseTime > this.alertConfig.thresholds.responseTime) {
        await this.sendAlert('warning', service);
      }
    }
  }

  private async sendAlert(severity: 'critical' | 'warning', service: ServiceStatus, consecutiveFailures?: number): Promise<void> {
    const alertMessage = {
      severity,
      service: service.name,
      status: service.status,
      error: service.error,
      responseTime: service.responseTime,
      consecutiveFailures,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
    };

    // Log alert
    console.error(`[ALERT] ${severity.toUpperCase()}: Service ${service.name} is ${service.status}`, alertMessage);

    // Send email alert
    if (this.alertConfig.emailRecipients.length > 0) {
      try {
        for (const recipient of this.alertConfig.emailRecipients) {
          await EmailService.sendAlertEmail(recipient, alertMessage);
        }
      } catch (error) {
        console.error('Failed to send alert email:', error);
      }
    }

    // Send webhook alert
    if (this.alertConfig.webhookUrl) {
      try {
        await fetch(this.alertConfig.webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(alertMessage),
        });
      } catch (error) {
        console.error('Failed to send webhook alert:', error);
      }
    }
  }

  async getMetrics(): Promise<any> {
    const health = await this.checkSystemHealth();
    
    return {
      health,
      metrics: {
        uptime: health.uptime,
        serviceFailures: Object.fromEntries(this.serviceFailures),
        alertConfig: {
          enabled: this.alertConfig.enabled,
          recipientCount: this.alertConfig.emailRecipients.length,
          thresholds: this.alertConfig.thresholds,
        },
      },
    };
  }
}

export default MonitoringService;
