"use client"

import { useEffect, useRef } from "react"

export function PieChartComponent() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    // Data for the pie chart
    const data = [
      { value: 374.82, color: "#3b82f6", label: "Website" },
      { value: 241.6, color: "#8b5cf6", label: "Mobile App" },
      { value: 213.42, color: "#14b8a6", label: "Other" },
    ]

    const total = data.reduce((sum, item) => sum + item.value, 0)

    // Draw the pie chart
    const centerX = canvas.width / 2
    const centerY = canvas.height / 2
    const radius = Math.min(centerX, centerY) * 0.8

    let startAngle = -0.5 * Math.PI // Start from the top

    data.forEach((item) => {
      const sliceAngle = (item.value / total) * 2 * Math.PI

      ctx.beginPath()
      ctx.moveTo(centerX, centerY)
      ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle)
      ctx.closePath()

      ctx.fillStyle = item.color
      ctx.fill()

      startAngle += sliceAngle
    })
  }, [])

  return <canvas ref={canvasRef} className="h-full w-full"></canvas>
}
