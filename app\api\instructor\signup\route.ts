import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifyInstructorSession, createInstructorSession } from '@/app/lib/instructor-session';
import { verifyInstructorTokenSession } from '@/app/lib/instructor-token-session';
import bcrypt from 'bcrypt'
// POST: Complete instructor signup
export async function POST(req: NextRequest) {
  const session = await verifyInstructorTokenSession();
  if (!session) {
    return NextResponse.json({ success: false, error: 'No valid instructor session' }, { status: 401 });
  }
  const { fullName, email, password } = await req.json();
  if (!fullName || !email || !password) {
    return NextResponse.json({ success: false, error: 'Missing fields' }, { status: 400 });
  }
  // Check if instructor already exists
  const existing = await prisma.instructor.findFirst({ where: { email } });
  if (existing) {
    return NextResponse.json({ success: false, error: 'Instructor already exists' }, { status: 409 });
  }
  // Create instructor
  const hashedPassword = await bcrypt.hash(password,10)
  const instructor = await prisma.instructor.create({
    data: {
      name: fullName,
      email,
      password, // Hash in production!
      univ: { connect: { id: session.univId } },
    },
  });
  // Update session with real instructor id/email
  await createInstructorSession({
    id: instructor.id,
    email: instructor.email,
    role: 'instructor',
    university: { id: session.univId },
  });
  await prisma.instructorToken.update({
    where: {
      code: session.token,
      univId: session.univId
    },
    data: {
      usageCount: { increment: 1 }
    }
  })
  return NextResponse.json({ success: true, instructor });
}
