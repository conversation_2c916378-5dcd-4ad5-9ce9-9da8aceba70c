import { PrismaClient } from '@prisma/client';
import { sendReceiptEmail, sendPaymentFailureEmail } from '@/utils/mailer';
import prisma from '@/app/lib/prisma';


export class EmailService {
  /**
   * Send receipt email with duplicate prevention
   */
  static async sendReceiptEmailSafely(
    email: string,
    transactionId: string,
    amount: number,
    paymentMethod: string,
    currency: string = 'XAF'
  ): Promise<{ success: boolean; message: string; alreadySent?: boolean }> {
    try {
      // Check if email was already sent for this transaction
      const existingPayment = await prisma.payment.findFirst({
        where: {
          token: transactionId,
          status: 'completed',
        },
      });

      if (!existingPayment) {
        return {
          success: false,
          message: 'Payment not found or not completed',
        };
      }

      // For now, we'll use a simple check based on payment status
      // In a full implementation, we'd have a separate email_logs table
      
      // Check if this is the first time we're processing this completed payment
      // by checking if the payment was just updated to 'completed'
      
      console.log('Sending receipt email for completed payment:', {
        email,
        transactionId,
        amount,
        paymentMethod,
        currency,
      });

      // Send the receipt email
      await sendReceiptEmail(email, transactionId, amount, paymentMethod, currency);

      console.log('Receipt email sent successfully to:', email);

      return {
        success: true,
        message: 'Receipt email sent successfully',
      };
    } catch (error: any) {
      console.error('Failed to send receipt email:', error);
      return {
        success: false,
        message: error.message || 'Failed to send receipt email',
      };
    }
  }

  /**
   * Send payment confirmation email (for successful payments)
   */
  static async sendPaymentConfirmationEmail(
    email: string,
    transactionId: string,
    amount: number,
    currency: string,
    planName: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Sending payment confirmation email:', {
        email,
        transactionId,
        amount,
        currency,
        planName,
      });

      // Use the existing receipt email function
      await sendReceiptEmail(email, transactionId, amount, 'Flutterwave', planName);

      console.log('Payment confirmation email sent successfully to:', email);

      return {
        success: true,
        message: 'Payment confirmation email sent successfully',
      };
    } catch (error: any) {
      console.error('Failed to send payment confirmation email:', error);
      return {
        success: false,
        message: error.message || 'Failed to send payment confirmation email',
      };
    }
  }

  /**
   * Send payment failure notification email
   */
  static async sendPaymentFailureEmail(
    email: string,
    transactionId: string,
    amount: number,
    currency: string,
    reason: string,
    paymentMethod?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Sending payment failure email:', {
        email,
        transactionId,
        amount,
        currency,
        reason,
        paymentMethod,
      });

      // Send the actual payment failure email
      await sendPaymentFailureEmail(
        email,
        transactionId,
        amount,
        currency,
        reason,
        paymentMethod
      );

      console.log('Payment failure email sent successfully to:', email);

      return {
        success: true,
        message: 'Payment failure email sent successfully',
      };
    } catch (error: any) {
      console.error('Failed to send payment failure email:', error);
      return {
        success: false,
        message: error.message || 'Failed to send payment failure email',
      };
    }
  }

  /**
   * Check if receipt email was already sent for a transaction
   */
  static async wasReceiptEmailSent(transactionId: string): Promise<boolean> {
    try {
      // Check if payment exists and is completed
      const payment = await prisma.payment.findFirst({
        where: {
          token: transactionId,
          status: 'completed',
        },
      });

      // If payment exists and is completed, we assume email was sent
      // In a full implementation, we'd have a separate email_logs table
      return !!payment;
    } catch (error) {
      console.error('Failed to check email status:', error);
      return false;
    }
  }

  /**
   * Send system alert email to administrators
   */
  static async sendAlertEmail(recipient: string, alertData: any): Promise<EmailResult> {
    try {
      if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
        return {
          success: false,
          message: 'Email service not configured',
        };
      }

      const transporter = nodemailer.createTransporter({
        service: 'gmail',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS,
        },
      });

      const severityColor = alertData.severity === 'critical' ? '#dc2626' : '#f59e0b';
      const severityIcon = alertData.severity === 'critical' ? '🚨' : '⚠️';

      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: recipient,
        subject: `${severityIcon} SmartOnline System Alert - ${alertData.service} ${alertData.status}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: ${severityColor}; color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0;">${severityIcon} System Alert</h1>
              <p style="margin: 5px 0 0 0; font-size: 18px;">${alertData.severity.toUpperCase()}</p>
            </div>

            <div style="padding: 20px; background-color: #f9fafb;">
              <h2 style="color: #374151; margin-top: 0;">Service Status Alert</h2>

              <div style="background-color: white; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h3 style="color: #374151; margin-top: 0;">Alert Details</h3>
                <ul style="color: #6b7280; line-height: 1.6;">
                  <li><strong>Service:</strong> ${alertData.service}</li>
                  <li><strong>Status:</strong> ${alertData.status}</li>
                  <li><strong>Severity:</strong> ${alertData.severity}</li>
                  <li><strong>Timestamp:</strong> ${alertData.timestamp}</li>
                  <li><strong>Environment:</strong> ${alertData.environment}</li>
                  ${alertData.error ? `<li><strong>Error:</strong> ${alertData.error}</li>` : ''}
                  ${alertData.responseTime ? `<li><strong>Response Time:</strong> ${alertData.responseTime}ms</li>` : ''}
                  ${alertData.consecutiveFailures ? `<li><strong>Consecutive Failures:</strong> ${alertData.consecutiveFailures}</li>` : ''}
                </ul>
              </div>

              <div style="background-color: white; padding: 15px; border-radius: 8px;">
                <h3 style="color: #374151; margin-top: 0;">Recommended Actions</h3>
                <ul style="color: #6b7280; line-height: 1.6;">
                  ${alertData.service === 'database' ? `
                    <li>Check database connectivity and server status</li>
                    <li>Verify database credentials and connection string</li>
                    <li>Monitor database performance metrics</li>
                  ` : ''}
                  ${alertData.service === 'email' ? `
                    <li>Verify email service credentials</li>
                    <li>Check SMTP server connectivity</li>
                    <li>Review email sending quotas and limits</li>
                  ` : ''}
                  ${alertData.service === 'payment_gateway' ? `
                    <li>Check Flutterwave API status</li>
                    <li>Verify payment gateway credentials</li>
                    <li>Monitor payment processing rates</li>
                  ` : ''}
                  ${alertData.service === 'external_dependencies' ? `
                    <li>Check internet connectivity</li>
                    <li>Verify DNS resolution</li>
                    <li>Monitor network latency</li>
                  ` : ''}
                  <li>Check system logs for additional details</li>
                  <li>Visit the monitoring dashboard for real-time status</li>
                </ul>
              </div>
            </div>

            <div style="background-color: #374151; color: white; padding: 15px; text-align: center;">
              <p style="margin: 0; font-size: 14px;">
                SmartOnline System Monitoring
                <br>
                <a href="${process.env.NEXT_PUBLIC_BASE_URL}/api/status" style="color: #60a5fa;">View System Status</a>
              </p>
            </div>
          </div>
        `,
      };

      await transporter.sendMail(mailOptions);

      return {
        success: true,
        message: 'Alert email sent successfully',
      };
    } catch (error: any) {
      console.error('Failed to send alert email:', error);
      return {
        success: false,
        message: error.message || 'Failed to send alert email',
      };
    }
  }

  /**
   * Get email sending statistics
   */
  static async getEmailStats(): Promise<{
    totalPayments: number;
    completedPayments: number;
    emailsSent: number;
  }> {
    try {
      const totalPayments = await prisma.payment.count();
      const completedPayments = await prisma.payment.count({
        where: { status: 'completed' },
      });

      // For now, assume emails were sent for all completed payments
      const emailsSent = completedPayments;

      return {
        totalPayments,
        completedPayments,
        emailsSent,
      };
    } catch (error) {
      console.error('Failed to get email stats:', error);
      return {
        totalPayments: 0,
        completedPayments: 0,
        emailsSent: 0,
      };
    }
  }
}

// Helper function for backward compatibility
export async function sendReceiptEmailSafely(
  email: string,
  transactionId: string,
  amount: number,
  paymentMethod: string,
  currency: string = 'XAF'
): Promise<void> {
  const result = await EmailService.sendReceiptEmailSafely(
    email,
    transactionId,
    amount,
    paymentMethod,
    currency
  );

  if (!result.success) {
    throw new Error(result.message);
  }
}
