"use client"

import { useState } from "react"
import {
  BookOpen,
  Search,
  ChevronRight,
  ChevronDown,
  Bell,
  HelpCircle,
  MessageSquare,
  FileText,
  Video,
  ThumbsUp,
  ThumbsDown,
  Send,
  X,
  Star,
  Users,
  Clock,
} from "lucide-react"

export default function HelpCenterPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [expandedCategories, setExpandedCategories] = useState<string[]>(["getting-started"])
  const [selectedArticle, setSelectedArticle] = useState<any>(null)
  const [showContactForm, setShowContactForm] = useState(false)

  const categories = [
    {
      id: "getting-started",
      name: "Getting Started",
      icon: BookOpen,
      articles: [
        {
          id: "1",
          title: "Creating your first exam",
          description: "Step-by-step guide to creating and publishing your first exam",
          readTime: "5 min read",
          helpful: 24,
          content: `# Creating Your First Exam

Welcome to ExamPro! This guide will walk you through creating and publishing your first exam.

## Step 1: Navigate to My Exams
1. Click on "My Exams" in the sidebar
2. Click the "Create New Exam" button

## Step 2: Basic Information
- Enter your exam title
- Select the course
- Set the duration
- Choose the difficulty level

## Step 3: Add Questions
You can add questions in several ways:
- Create new questions from scratch
- Import from your question bank
- Upload questions from a file

## Step 4: Configure Settings
- Set availability dates
- Configure proctoring options
- Set password protection if needed

## Step 5: Preview and Publish
Before publishing, use the preview feature to see how your exam will look to students.

Once you're satisfied, click "Publish" to make your exam available to students.`,
        },
        {
          id: "2",
          title: "Setting up your profile",
          description: "Complete your instructor profile for better visibility",
          readTime: "3 min read",
          helpful: 18,
          content: "Profile setup content...",
        },
        {
          id: "3",
          title: "Adding students to your course",
          description: "Learn how to enroll students and manage course rosters",
          readTime: "4 min read",
          helpful: 31,
          content: "Student enrollment content...",
        },
      ],
    },
    {
      id: "exam-creation",
      name: "Exam Creation",
      icon: FileText,
      articles: [
        {
          id: "4",
          title: "Question types and best practices",
          description: "Understanding different question types and when to use them",
          readTime: "8 min read",
          helpful: 42,
          content: "Question types content...",
        },
        {
          id: "5",
          title: "Setting up online proctoring",
          description: "Configure secure online proctoring for your exams",
          readTime: "6 min read",
          helpful: 27,
          content: "Proctoring setup content...",
        },
        {
          id: "6",
          title: "Importing questions from other platforms",
          description: "How to migrate questions from other LMS platforms",
          readTime: "10 min read",
          helpful: 15,
          content: "Import questions content...",
        },
      ],
    },
    {
      id: "grading-results",
      name: "Grading & Results",
      icon: Star,
      articles: [
        {
          id: "7",
          title: "Automated vs manual grading",
          description: "When to use automated grading and when to grade manually",
          readTime: "5 min read",
          helpful: 33,
          content: "Grading methods content...",
        },
        {
          id: "8",
          title: "Understanding grade analytics",
          description: "Interpreting grade distributions and performance metrics",
          readTime: "7 min read",
          helpful: 21,
          content: "Analytics content...",
        },
      ],
    },
    {
      id: "account-management",
      name: "Account Management",
      icon: Users,
      articles: [
        {
          id: "9",
          title: "Changing your password",
          description: "How to update your account password and security settings",
          readTime: "2 min read",
          helpful: 12,
          content: "Password change content...",
        },
        {
          id: "10",
          title: "Two-factor authentication setup",
          description: "Secure your account with two-factor authentication",
          readTime: "4 min read",
          helpful: 19,
          content: "2FA setup content...",
        },
      ],
    },
    {
      id: "troubleshooting",
      name: "Troubleshooting",
      icon: HelpCircle,
      articles: [
        {
          id: "11",
          title: "Common login issues",
          description: "Solving problems with logging into your account",
          readTime: "3 min read",
          helpful: 28,
          content: "Login troubleshooting content...",
        },
        {
          id: "12",
          title: "Exam publishing problems",
          description: "What to do when your exam won't publish",
          readTime: "5 min read",
          helpful: 16,
          content: "Publishing problems content...",
        },
      ],
    },
  ]

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryId) ? prev.filter((id) => id !== categoryId) : [...prev, categoryId],
    )
  }

  const filteredCategories = categories.map((category) => ({
    ...category,
    articles: category.articles.filter(
      (article) =>
        article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        article.description.toLowerCase().includes(searchTerm.toLowerCase()),
    ),
  }))

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-600 text-white">
                  <BookOpen className="h-4 w-4" />
                </div>
                <span className="text-xl font-bold text-blue-600">ExamPro</span>
              </div>
              <div className="hidden md:block h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">Help Center</h1>
            </div>
            <div className="flex items-center gap-4">
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <Bell className="h-5 w-5" />
              </button>
              <button
                onClick={() => setShowContactForm(true)}
                className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                <MessageSquare className="h-4 w-4" />
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <div className="max-w-6xl mx-auto">
          {selectedArticle ? (
            <ArticleView article={selectedArticle} onBack={() => setSelectedArticle(null)} />
          ) : (
            <>
              {/* Hero Section */}
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold text-gray-900 mb-4">How can we help you?</h2>
                <p className="text-xl text-gray-600 mb-8">Search our knowledge base or browse categories below</p>

                {/* Search Bar */}
                <div className="max-w-2xl mx-auto">
                  <div className="relative">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search for articles, guides, or tutorials..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg"
                    />
                  </div>
                  {searchTerm && (
                    <div className="mt-2 text-sm text-gray-600">
                      Found {filteredCategories.reduce((total, category) => total + category.articles.length, 0)}{" "}
                      article
                      {filteredCategories.reduce((total, category) => total + category.articles.length, 0) !== 1
                        ? "s"
                        : ""}{" "}
                      matching "{searchTerm}"
                    </div>
                  )}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="grid gap-6 md:grid-cols-3 mb-12">
                <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
                      <Video className="h-5 w-5 text-green-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Video Tutorials</h3>
                  </div>
                  <p className="text-gray-600">Watch step-by-step video guides for common tasks</p>
                </div>

                <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Community Forum</h3>
                  </div>
                  <p className="text-gray-600">Connect with other instructors and share best practices</p>
                </div>

                <div
                  onClick={() => setShowContactForm(true)}
                  className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100">
                      <MessageSquare className="h-5 w-5 text-purple-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Contact Support</h3>
                  </div>
                  <p className="text-gray-600">Get direct help from our support team</p>
                </div>
              </div>

              {/* Knowledge Base Categories */}
              <div className="grid gap-6 lg:grid-cols-2">
                {filteredCategories.map((category) => {
                  const Icon = category.icon
                  const isExpanded = expandedCategories.includes(category.id)

                  return (
                    <div key={category.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                      <button
                        onClick={() => toggleCategory(category.id)}
                        className="w-full flex items-center justify-between p-6 hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                            <Icon className="h-5 w-5 text-blue-600" />
                          </div>
                          <div className="text-left">
                            <h3 className="text-lg font-semibold text-gray-900">{category.name}</h3>
                            <p className="text-sm text-gray-600">{category.articles.length} articles</p>
                          </div>
                        </div>
                        {isExpanded ? (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        ) : (
                          <ChevronRight className="h-5 w-5 text-gray-400" />
                        )}
                      </button>

                      {isExpanded && (
                        <div className="border-t border-gray-200">
                          {category.articles.map((article) => (
                            <button
                              key={article.id}
                              onClick={() => setSelectedArticle(article)}
                              className="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors text-left border-b border-gray-100 last:border-b-0"
                            >
                              <div className="flex-1">
                                <h4 className="font-medium text-gray-900">{article.title}</h4>
                                <p className="text-sm text-gray-600 mt-1">{article.description}</p>
                                <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                                  <span className="flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    {article.readTime}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <ThumbsUp className="h-3 w-3" />
                                    {article.helpful} helpful
                                  </span>
                                </div>
                              </div>
                              <ChevronRight className="h-4 w-4 text-gray-400 ml-4" />
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </>
          )}
        </div>
      </main>

      {/* Contact Support Modal */}
      {showContactForm && <ContactSupportModal onClose={() => setShowContactForm(false)} />}
    </div>
  )
}

function ArticleView({ article, onBack }: { article: any; onBack: () => void }) {
  const [helpful, setHelpful] = useState<boolean | null>(null)

  return (
    <div className="max-w-4xl mx-auto">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-sm text-gray-600 mb-6">
        <button onClick={onBack} className="hover:text-blue-600 transition-colors">
          Help Center
        </button>
        <ChevronRight className="h-4 w-4" />
        <span>{article.title}</span>
      </div>

      {/* Article Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-8 mb-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{article.title}</h1>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                {article.readTime}
              </span>
              <span className="flex items-center gap-1">
                <ThumbsUp className="h-4 w-4" />
                {article.helpful} people found this helpful
              </span>
            </div>
          </div>
          <button
            onClick={onBack}
            className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors"
          >
            ← Back to Help Center
          </button>
        </div>

        {/* Article Content */}
        <div className="prose prose-lg max-w-none">
          <div className="whitespace-pre-wrap">{article.content}</div>
        </div>

        {/* Feedback */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Was this article helpful?</h3>
          <div className="flex items-center gap-3">
            <button
              onClick={() => setHelpful(true)}
              className={`inline-flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                helpful === true
                  ? "bg-green-100 text-green-800 border border-green-300"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              <ThumbsUp className="h-4 w-4" />
              Yes
            </button>
            <button
              onClick={() => setHelpful(false)}
              className={`inline-flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                helpful === false
                  ? "bg-red-100 text-red-800 border border-red-300"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              <ThumbsDown className="h-4 w-4" />
              No
            </button>
          </div>
          {helpful !== null && (
            <p className="text-sm text-gray-600 mt-2">
              {helpful
                ? "Thank you for your feedback!"
                : "We're sorry this wasn't helpful. Please contact support for more help."}
            </p>
          )}
        </div>
      </div>

      {/* Related Articles */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Related Articles</h3>
        <div className="space-y-3">
          {[
            { title: "Advanced question settings", readTime: "4 min read" },
            { title: "Bulk importing students", readTime: "6 min read" },
            { title: "Setting up grade categories", readTime: "5 min read" },
          ].map((relatedArticle, index) => (
            <button
              key={index}
              className="w-full flex items-center justify-between p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors text-left"
            >
              <div>
                <div className="font-medium text-gray-900">{relatedArticle.title}</div>
                <div className="text-sm text-gray-600">{relatedArticle.readTime}</div>
              </div>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}

function ContactSupportModal({ onClose }: { onClose: () => void }) {
  const [priority, setPriority] = useState("medium")
  const [subject, setSubject] = useState("")
  const [description, setDescription] = useState("")

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Contact Support</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <form className="space-y-6">
          {/* Priority */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
            <select
              value={priority}
              onChange={(e) => setPriority(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="low">Low - General question</option>
              <option value="medium">Medium - Need help with a feature</option>
              <option value="high">High - Can't complete important task</option>
              <option value="urgent">Urgent - System is not working</option>
            </select>
          </div>

          {/* Subject */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
            <input
              type="text"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Brief description of your issue"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
            <textarea
              rows={6}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Please provide as much detail as possible..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Attachments */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Attachments (optional)</label>
            <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
              <p className="text-sm text-gray-600">Drop files here or click to upload</p>
              <p className="text-xs text-gray-500 mt-1">PNG, JPG, PDF up to 10MB</p>
              <input type="file" className="hidden" multiple />
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Send className="h-4 w-4" />
              Submit Ticket
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
