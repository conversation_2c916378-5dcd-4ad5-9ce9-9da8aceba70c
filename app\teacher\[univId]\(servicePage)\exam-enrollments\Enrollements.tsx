'use client'

import { useEffect, useState } from 'react'
import axios from 'axios'
import { CheckCircle, XCircle, Search } from 'lucide-react'
import { updateEnrollmentStatus } from '../action'
import { toast } from 'sonner'

interface Enrollment {
  id: string
  student: { id: string; name: string }
  exam: { id: string; title: string }
  enrolledAt: string
  status: 'pending' | 'approved' | 'rejected'
}

const PAGE_SIZE = 5

export default function StudentEnrollmentTable({ Enrollments }: { Enrollments: Enrollment[] }) {
  const [enrollments, setEnrollments] = useState<Enrollment[]>(Enrollments || [])
  const [filtered, setFiltered] = useState<Enrollment[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
    console.log(Enrollments)
  useEffect(() => {
    handleSearch()
  }, [searchTerm, enrollments])

  
  const handleAction = async (id: string, action: 'approve' | 'reject') => {
    const res =await updateEnrollmentStatus(id, action)
    if (res.success) {
      setEnrollments(prev => prev.map(e => e.id === id ? { ...e, status: action === 'approve' ? 'approved' : 'rejected' } : e))
      handleSearch() // Re-filter after action
    }
    else {
      toast.error( res.error)
    }
    
  }

  const handleSearch = () => {
    const term = searchTerm.toLowerCase()
    const result = enrollments.filter(e =>
      e.student.name.toLowerCase().includes(term) ||
      e.exam.title.toLowerCase().includes(term)
    )
    setFiltered(result)
    setCurrentPage(1)
  }

  const currentData = filtered.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE)
  const totalPages = Math.ceil(filtered.length / PAGE_SIZE)

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Student Enrollment Requests</h2>

      <div className="mb-4 flex items-center gap-3">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-3 top-2.5 text-gray-400 w-4 h-4" />
          <input
            type="text"
            className="pl-9 pr-3 py-2 border border-gray-300 rounded-md w-full text-sm focus:ring-2 focus:ring-indigo-300"
            placeholder="Search student or exam..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      { currentData.length === 0 ? (
        <p>No matching enrollment requests.</p>
      ) : (
        <div className="overflow-auto">
          <table className="min-w-full border text-sm text-gray-700">
            <thead className="bg-gray-100">
              <tr>
                <th className="p-3 border">Student</th>
                <th className="p-3 border">Exam</th>
                <th className="p-3 border">Date</th>
                <th className="p-3 border">Status</th>
                <th className="p-3 border text-center">Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentData.map(enroll => (
                <tr key={enroll.id} className="border-b hover:bg-gray-50">
                  <td className="p-3 border">{enroll.student.name}</td>
                  <td className="p-3 border">{enroll.exam.title}</td>
                  <td className="p-3 border">
                    {new Date(enroll.enrolledAt).toLocaleString()}
                  </td>
                  <td className="p-3 border capitalize">{enroll.status}</td>
                  <td className="p-3 border text-center space-x-2">
                    {enroll.status === 'pending' ? (
                      <>
                        <button
                          onClick={() => handleAction(enroll.id, 'approve')}
                          className="px-3 py-1 bg-emerald-500 text-white rounded hover:bg-emerald-600"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => handleAction(enroll.id, 'reject')}
                          className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                        >
                          Reject
                        </button>
                      </>
                    ) : enroll.status === 'approved' ? (
                      <CheckCircle className="text-green-500 w-5 h-5 inline-block" />
                    ) : (
                      <XCircle className="text-red-500 w-5 h-5 inline-block" />
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {totalPages > 1 && (
        <div className="flex justify-between items-center mt-4">
          <button
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
            className="px-4 py-2 text-sm bg-gray-100 rounded hover:bg-gray-200 disabled:opacity-50"
            disabled={currentPage === 1}
          >
            Previous
          </button>
          <span className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
            className="px-4 py-2 text-sm bg-gray-100 rounded hover:bg-gray-200 disabled:opacity-50"
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}
