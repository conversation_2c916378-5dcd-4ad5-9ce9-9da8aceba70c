"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { toast } from "sonner"

type QuestionType = "input" | "textarea" | "radio" | "checkbox"

interface AnswerOption {
  id: string
  text: string
}

interface Question {
  id: string
  text: string
  type: QuestionType
  required: boolean
  options: AnswerOption[]
  saved: boolean
  savedAt?: string
}

interface ExamData {
  title: string
  description: string
  duration: number
  startDate: string
  startTime: string
  courseId: string // NEW
  questions: Question[]
}

export default function CreateExam({ teacherId }: { teacherId: string }) {
  const [examData, setExamData] = useState<ExamData>({
    title: "",
    description: "",
    duration: 60,
    startDate: "",
    startTime: "",
    courseId: "", // NEW
    questions: [],
  })

  const [expandedQuestions, setExpandedQuestions] = useState<Set<string>>(new Set())
  const [courses, setCourses] = useState<{ id: string; name: string }[]>([])
  const [loadingCourses, setLoadingCourses] = useState(false)

  useEffect(() => {
    async function fetchCourses() {
      setLoadingCourses(true)
      try {
        const res = await fetch(`/api/teacher/${teacherId}/courses`)
        if (!res.ok) throw new Error("Failed to fetch courses")
        const data = await res.json()
        setCourses(data.courses || [])
      } catch (err) {
        setCourses([])
      } finally {
        setLoadingCourses(false)
      }
    }
    if (teacherId) fetchCourses()
  }, [teacherId])

  const addQuestion = () => {
    const newQuestion: Question = {
      id: `question-${Date.now()}`,
      text: "",
      type: "input",
      required: false,
      options: [],
      saved: false,
    }
    setExamData((prev) => ({
      ...prev,
      questions: [...prev.questions, newQuestion],
    }))
    setExpandedQuestions((prev) => new Set([...prev, newQuestion.id]))
  }

  const removeQuestion = (questionId: string) => {
    setExamData((prev) => ({
      ...prev,
      questions: prev.questions.filter((q) => q.id !== questionId),
    }))
    setExpandedQuestions((prev) => {
      const newSet = new Set(prev)
      newSet.delete(questionId)
      return newSet
    })
  }

  const updateQuestion = (questionId: string, updates: Partial<Question>) => {
    setExamData((prev) => ({
      ...prev,
      questions: prev.questions.map((q) => (q.id === questionId ? { ...q, ...updates } : q)),
    }))
  }

  // Save a single question to the backend
  const saveQuestion = async (questionId: string) => {
    const question = examData.questions.find((q) => q.id === questionId)
    if (!question || !question.text.trim()) {
      toast.error("Please add question text before saving")
      return
    }
    if (question.type === "radio" || question.type === "checkbox") {
      const validOptions = question.options.filter((opt) => opt.text.trim())
      if (validOptions.length < 2) {
        toast.error("Please add at least 2 answer options before saving")
        return
      }
    }
    // Call API to save question (optional, if you want to persist individual questions)
    try {
      const res = await fetch(`/api/teacher/${teacherId}/exam`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "save-question",
          question,
        }),
      })
      if (!res.ok) throw new Error("Failed to save question")
      updateQuestion(questionId, {
        saved: true,
        savedAt: new Date().toISOString(),
      })
      toast.error("Question saved successfully! It will be preserved permanently.")
    } catch (err) {
      toast.error("Failed to save question. Please try again.")
    }
  }

  const addAnswerOption = (questionId: string) => {
    const newOption: AnswerOption = {
      id: `option-${Date.now()}`,
      text: "",
    }
    setExamData((prev) => ({
      ...prev,
      questions: prev.questions.map((q) => (q.id === questionId ? { ...q, options: [...q.options, newOption] } : q)),
    }))
  }

  const removeAnswerOption = (questionId: string, optionId: string) => {
    setExamData((prev) => ({
      ...prev,
      questions: prev.questions.map((q) =>
        q.id === questionId ? { ...q, options: q.options.filter((opt) => opt.id !== optionId) } : q,
      ),
    }))
  }

  const updateAnswerOption = (questionId: string, optionId: string, text: string) => {
    setExamData((prev) => ({
      ...prev,
      questions: prev.questions.map((q) =>
        q.id === questionId
          ? {
              ...q,
              options: q.options.map((opt) => (opt.id === optionId ? { ...opt, text } : opt)),
            }
          : q,
      ),
    }))
  }

  const handleQuestionTypeChange = (questionId: string, type: QuestionType) => {
    const updates: Partial<Question> = { type }

    if (
      (type === "radio" || type === "checkbox") &&
      !examData.questions.find((q) => q.id === questionId)?.options.length
    ) {
      updates.options = [
        { id: `option-${Date.now()}-1`, text: "" },
        { id: `option-${Date.now()}-2`, text: "" },
      ]
    }

    if (type === "input" || type === "textarea") {
      updates.options = []
    }

    updateQuestion(questionId, updates)
  }

  // Submit the entire exam to the backend
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    // Prepare exam payload
    const payload = {
      title: examData.title,
      description: examData.description,
      duration: examData.duration,
      startDate: examData.startDate,
      startTime: examData.startTime,
      courseId: examData.courseId, // NEW
      questions: examData.questions.map((q) => ({
        text: q.text,
        type: q.type,
        required: q.required,
        options: q.options,
      })),
    }
    try {
      const res = await fetch(`/api/teacher/${teacherId}/exam`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      })
      if (!res.ok) {
        const error = await res.json()
        toast.error("Failed to create exam: " + (error.message || res.statusText))
        return
      }
      const data = await res.json()
      toast.error("Exam created successfully!")
      // Optionally reset form or redirect
      // setExamData({ ... })
    } catch (err: any) {
      toast.error("An error occurred: " + (err.message || err))
    }
  }

  const isChoiceType = (type: QuestionType) => type === "radio" || type === "checkbox"

  const toggleQuestionExpanded = (questionId: string) => {
    setExpandedQuestions((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(questionId)) {
        newSet.delete(questionId)
      } else {
        newSet.add(questionId)
      }
      return newSet
    })
  }

  const expandAllQuestions = () => {
    setExpandedQuestions(new Set(examData.questions.map((q) => q.id)))
  }

  const collapseAllQuestions = () => {
    setExpandedQuestions(new Set())
  }

  const canSaveQuestion = (question: Question) => {
    if (!question.text.trim()) return false
    if (question.type === "radio" || question.type === "checkbox") {
      const validOptions = question.options.filter((opt) => opt.text.trim())
      return validOptions.length >= 2
    }
    return true
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto p-8">
        {/* Header */}
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-3">Create New Exam</h1>
          <p className="text-sm text-gray-600">Design your exam with dynamic questions and flexible options</p>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-12 gap-4">
          {/* Left Column - Exam Details */}
          <div className="xl:col-span-4">
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden sticky top-8 shadow-sm">
              <div className="p-10">
                <div className="mb-8">
                  <h2 className="text-xl font-bold text-gray-900 mb-2">Exam Settings</h2>
                  <p className="text-gray-500">Configure your exam details</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-8">
                  <div>
                    <label className="block text-sm font-semibold text-gray-900 mb-3">Exam Title</label>
                    <input
                      type="text"
                      value={examData.title}
                      onChange={(e) => setExamData((prev) => ({ ...prev, title: e.target.value }))}
                      placeholder="Enter exam title"
                      className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 placeholder-gray-400 text-sm"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 ">
                    <div>
                      <label className="block text-sm font-semibold text-gray-900 mb-3">Duration</label>
                      <div className="relative">
                        <input
                          type="number"
                          value={examData.duration}
                          onChange={(e) =>
                            setExamData((prev) => ({ ...prev, duration: e.target.value == '' ? 0 : Number.parseInt(e.target.value) || 60 }))
                          }
                          placeholder="60"
                          min="1"
                          className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 text-sm"
                          required
                        />
                        <span className="absolute right-4 top-4 text-gray-400 font-medium">min</span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-900 mb-3">Questions</label>
                      <div className="px-5 py-4 bg-gray-50 border border-gray-200 rounded-lg text-gray-700 font-semibold text-sm">
                        {examData.questions.length}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-900 mb-3">Start Date</label>
                      <input
                        type="date"
                        value={examData.startDate}
                        onChange={(e) => setExamData((prev) => ({ ...prev, startDate: e.target.value }))}
                        min={new Date().toISOString().split("T")[0]}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 text-sm"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-900 mb-3">Start Time</label>
                      <input
                        type="time"
                        value={examData.startTime}
                        onChange={(e) => setExamData((prev) => ({ ...prev, startTime: e.target.value }))}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 text-sm"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-900 mb-3">Description</label>
                    <textarea
                      value={examData.description}
                      onChange={(e) => setExamData((prev) => ({ ...prev, description: e.target.value }))}
                      placeholder="Add exam instructions or description..."
                      rows={4}
                      className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 placeholder-gray-400 resize-none text-sm"
                    />
                  </div>

                  {/* Course Selection */}
                  <div>
                    <label className="block text-sm font-semibold text-gray-900 mb-3">Course</label>
                    <select
                      value={examData.courseId}
                      onChange={e => setExamData(prev => ({ ...prev, courseId: e.target.value }))}
                      className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 text-sm"
                      required
                      disabled={loadingCourses}
                    >
                      <option value="">{loadingCourses ? "Loading courses..." : "Select a course"}</option>
                      {courses.map(course => (
                        <option key={course.id} value={course.id}>{course.name}</option>
                      ))}
                    </select>
                  </div>

                  {/* Question Statistics */}
                  {examData.questions.length > 0 && (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                      <h3 className="text-sm font-bold text-gray-900 mb-4">Question Status</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Total Questions:</span>
                          <span className="font-semibold text-gray-900">{examData.questions.length}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Saved Questions:</span>
                          <span className="font-semibold text-green-600">
                            {examData.questions.filter((q) => q.saved).length}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Temporary Questions:</span>
                          <span className="font-semibold text-orange-600">
                            {examData.questions.filter((q) => !q.saved).length}
                          </span>
                        </div>
                      </div>
                      <div className="mt-4 text-sm text-gray-500">
                        <p>
                          💡 Temporary questions are deleted after 30 days. Save important questions to preserve them
                          permanently.
                        </p>
                      </div>
                    </div>
                  )}

                  <button
                    type="submit"
                    disabled={
                      !examData.title || !examData.startDate || !examData.startTime || examData.questions.length === 0
                    }
                    className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-semibold py-2 px-6 rounded-md transition-all duration-200 shadow-sm hover:shadow-md disabled:shadow-none"
                  >
                    Create Exam
                  </button>
                </form>
              </div>
            </div>
          </div>

          {/* Right Column - Questions */}
          <div className="xl:col-span-8">
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
              <div className="p-10 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-bold text-gray-900 mb-2">Questions</h2>
                    <p className="text-gray-500">{examData.questions.length} questions added</p>
                  </div>

                  <div className="flex items-center gap-4">
                    {examData.questions.length > 0 && (
                      <>
                        <button
                          type="button"
                          onClick={expandAllQuestions}
                          className="text-gray-500 hover:text-gray-700 font-medium px-3 py-1.5 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                        >
                          Expand All
                        </button>
                        <button
                          type="button"
                          onClick={collapseAllQuestions}
                          className="text-gray-500 hover:text-gray-700 font-medium px-3 py-1.5 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                        >
                          Collapse All
                        </button>
                      </>
                    )}
                    <button
                      type="button"
                      onClick={addQuestion}
                      className="bg-green-600 hover:bg-green-700 text-white font-semibold px-5 py-2.5 rounded-md transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Add Question
                    </button>
                  </div>
                </div>
              </div>

              <div className="p-5">
                <div className="space-y-8">
                  {examData.questions.length === 0 && (
                    <div className="text-center py-6">
                      <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={1.5}
                            d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3">No questions yet</h3>
                      <p className="text-gray-500 mb-8 text-sm">
                        Start building your exam by adding your first question
                      </p>
                      <button
                        type="button"
                        onClick={addQuestion}
                        className="bg-green-600 hover:bg-green-700 text-white font-semibold px-6 py-2 rounded-md transition-all duration-200 shadow-sm hover:shadow-md inline-flex items-center gap-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        Add Your First Question
                      </button>
                    </div>
                  )}

                  {examData.questions.map((question, index) => {
                    const isExpanded = expandedQuestions.has(question.id)

                    return (
                      <div
                        key={question.id}
                        className={`bg-white border-l-2 rounded-lg overflow-hidden hover:shadow-md transition-all duration-200 ${
                          question.saved ? "border-green-200 bg-green-50/30" : "border-gray-200"
                        }`}
                      >
                        <div className="p-8">
                          <div className="flex items-start justify-between">
                            <div
                              className="flex items-center gap-6 cursor-pointer flex-1"
                              onClick={() => toggleQuestionExpanded(question.id)}
                            >
                              <div
                                className={`w-10 h-10 border rounded-lg flex items-center justify-center font-bold text-sm ${
                                  question.saved
                                    ? "bg-green-100 border-green-200 text-green-700"
                                    : "bg-gray-100 border-gray-200 text-gray-700"
                                }`}
                              >
                                {index + 1}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center gap-4 mb-2">
                                  <span className="text-sm font-bold text-gray-900">Question {index + 1}</span>
                                  {question.saved && (
                                    <span className="bg-green-100 text-green-700 text-sm font-bold px-3 py-1 rounded-full border border-green-200 flex items-center gap-1">
                                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M5 13l4 4L19 7"
                                        />
                                      </svg>
                                      Saved
                                    </span>
                                  )}
                                  {!question.saved && (
                                    <span className="bg-orange-100 text-orange-700 text-sm font-bold px-3 py-1 rounded-full border border-orange-200 flex items-center gap-1">
                                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                        />
                                      </svg>
                                      30 days
                                    </span>
                                  )}
                                  {question.required && (
                                    <span className="bg-red-50 text-red-700 text-sm font-bold px-3 py-1 rounded-full border border-red-200">
                                      Required
                                    </span>
                                  )}
                                  <span className="bg-blue-50 text-blue-700 text-sm font-bold px-3 py-1 rounded-full border border-blue-200">
                                    {question.type === "input"
                                      ? "Short Text"
                                      : question.type === "textarea"
                                        ? "Long Text"
                                        : question.type === "radio"
                                          ? "Single Choice"
                                          : "Multiple Choice"}
                                  </span>
                                </div>
                                {!isExpanded && (
                                  <p className="text-gray-600 line-clamp-2 text-sm">
                                    {question.text || "Click to add question text..."}
                                  </p>
                                )}
                              </div>
                              <button className="p-2 hover:bg-gray-50 rounded-lg transition-colors">
                                {isExpanded ? (
                                  <svg
                                    className="w-4 h-4 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M5 15l7-7 7 7"
                                    />
                                  </svg>
                                ) : (
                                  <svg
                                    className="w-4 h-4 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M19 9l-7 7-7-7"
                                    />
                                  </svg>
                                )}
                              </button>
                            </div>
                            <div className="flex items-center gap-2">
                              {!question.saved && (
                                <button
                                  type="button"
                                  onClick={() => saveQuestion(question.id)}
                                  disabled={!canSaveQuestion(question)}
                                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-semibold px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md disabled:shadow-none flex items-center gap-2 text-sm"
                                  title={
                                    canSaveQuestion(question)
                                      ? "Save this question permanently"
                                      : "Complete the question to save it"
                                  }
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                                    />
                                  </svg>
                                  Save
                                </button>
                              )}
                              <button
                                type="button"
                                onClick={() => removeQuestion(question.id)}
                                className="p-2 text-red-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                  />
                                </svg>
                              </button>
                            </div>
                          </div>

                          {isExpanded && (
                            <div className="mt-8 space-y-8">
                              <div>
                                <label className="block text-sm font-bold text-gray-900 mb-3">Question Text</label>
                                <textarea
                                  value={question.text}
                                  onChange={(e) => updateQuestion(question.id, { text: e.target.value })}
                                  placeholder="Enter your question here..."
                                  rows={3}
                                  className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 placeholder-gray-400 resize-none text-sm"
                                />
                              </div>

                              <div className="grid grid-cols-2 gap-6">
                                <div>
                                  <label className="block text-sm font-bold text-gray-900 mb-3">Question Type</label>
                                  <select
                                    value={question.type}
                                    onChange={(e) =>
                                      handleQuestionTypeChange(question.id, e.target.value as QuestionType)
                                    }
                                    className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 text-sm"
                                  >
                                    <option value="input">Short Text</option>
                                    <option value="textarea">Long Text</option>
                                    <option value="radio">Single Choice</option>
                                    <option value="checkbox">Multiple Choice</option>
                                  </select>
                                </div>

                                <div className="flex items-end">
                                  <label className="flex items-center gap-4 cursor-pointer">
                                    <input
                                      type="checkbox"
                                      checked={question.required}
                                      onChange={(e) => updateQuestion(question.id, { required: e.target.checked })}
                                      className="w-6 h-6 text-green-600 border-gray-300 rounded-lg focus:ring-green-500"
                                    />
                                    <span className="text-sm font-bold text-gray-900">Required field</span>
                                  </label>
                                </div>
                              </div>

                              {isChoiceType(question.type) && (
                                <div>
                                  <div className="flex items-center justify-between mb-6">
                                    <label className="block text-sm font-bold text-gray-900">Answer Options</label>
                                    <button
                                      type="button"
                                      onClick={() => addAnswerOption(question.id)}
                                      className="text-green-600 hover:text-green-700 font-semibold px-4 py-2 border border-green-200 rounded-lg hover:bg-green-50 transition-colors flex items-center gap-2 text-sm"
                                    >
                                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M12 4v16m8-8H4"
                                        />
                                      </svg>
                                      Add Option
                                    </button>
                                  </div>

                                  <div className="space-y-4">
                                    {question.options.map((option, optionIndex) => (
                                      <div key={option.id} className="flex items-center gap-4">
                                        <div className="w-10 h-10 bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center text-sm font-bold text-gray-600">
                                          {String.fromCharCode(65 + optionIndex)}
                                        </div>
                                        <input
                                          type="text"
                                          value={option.text}
                                          onChange={(e) => updateAnswerOption(question.id, option.id, e.target.value)}
                                          placeholder={`Option ${String.fromCharCode(65 + optionIndex)}`}
                                          className="flex-1 px-4 py-2 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 placeholder-gray-400 text-sm"
                                        />
                                        {question.options.length > 1 && (
                                          <button
                                            type="button"
                                            onClick={() => removeAnswerOption(question.id, option.id)}
                                            className="p-2 text-red-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                          >
                                            <svg
                                              className="w-4 h-4"
                                              fill="none"
                                              stroke="currentColor"
                                              viewBox="0 0 24 24"
                                            >
                                              <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                              />
                                            </svg>
                                          </button>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              <div className="bg-gray-50 border border-gray-200 rounded-lg p-8">
                                <h4 className="text-sm font-bold text-gray-900 mb-6">Preview</h4>
                                <div className="space-y-6">
                                  <p className="font-semibold text-gray-900 text-xl">
                                    {question.text || "Your question will appear here..."}
                                    {question.required && <span className="text-red-500 ml-2">*</span>}
                                  </p>

                                  {question.type === "input" && (
                                    <input
                                      type="text"
                                      placeholder="Student answer..."
                                      disabled
                                      className="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg text-gray-500 text-sm"
                                    />
                                  )}

                                  {question.type === "textarea" && (
                                    <textarea
                                      placeholder="Student detailed answer..."
                                      disabled
                                      rows={4}
                                      className="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg text-gray-500 resize-none text-sm"
                                    />
                                  )}

                                  {question.type === "radio" && (
                                    <div className="space-y-4">
                                      {question.options.map((option, optionIndex) => (
                                        <div key={option.id} className="flex items-center gap-4">
                                          <input type="radio" disabled className="w-5 h-5 text-green-600" />
                                          <span className="text-gray-700 text-sm">
                                            {option.text || `Option ${String.fromCharCode(65 + optionIndex)}`}
                                          </span>
                                        </div>
                                      ))}
                                    </div>
                                  )}

                                  {question.type === "checkbox" && (
                                    <div className="space-y-4">
                                      {question.options.map((option, optionIndex) => (
                                        <div key={option.id} className="flex items-center gap-4">
                                          <input type="checkbox" disabled className="w-5 h-5 text-green-600" />
                                          <span className="text-gray-700 text-sm">
                                            {option.text || `Option ${String.fromCharCode(65 + optionIndex)}`}
                                          </span>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
