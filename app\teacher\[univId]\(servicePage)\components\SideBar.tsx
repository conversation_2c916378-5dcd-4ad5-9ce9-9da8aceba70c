import {
  BookOpen,
  Menu,
  LayoutDashboard,
  Settings,
  FileText,
  TrendingUp,
  Calendar,
  ChevronDown,
  HelpCircle,
  Users,
  ClipboardList,
  Award,
} from "lucide-react";
import Link from "next/link";
import React from "react";

type LinkItem = {
  name: string;
  href: string;
  icon: React.FC<React.SVGProps<SVGSVGElement>>;
  notificationCount?: number | string;
  isActive?: boolean;
};

const SideBar = ({univId}:{univId:string}) => {
  // === MAIN LINKS ===
  const mainLinks: LinkItem[] = [
    {
      name: "Dashboard",
      href: "dashboard",
      icon: LayoutDashboard,
      isActive: true, // will render with green bg/text
    },
    {
      name: "My Exams",
      href: "my-exams",
      icon: ClipboardList,
    },
    {
      name: "Exam enrollments",
      href: "exam-enrollments",
      icon: Users,
    },
    {
      name: "Students",
      href: "students",
      icon: Users,
    },
  ];
  
  // === CONTENT LINKS ===
  const contentLinks: LinkItem[] = [
    {
      name: "Results",
      href: "results",
      icon: Award,
    },
    {
      name: "Question Bank",
      href: "question-bank",
      icon: BookOpen,
    },
    // {
    //   name: "Reports",
    //   href: "reports",
    //   icon: FileText,
    // },
    {
      name: "Analytics",
      href: "analytics",
      icon: TrendingUp,
    },
    {
      name: "Schedule",
      href: "schedule",
      icon: Calendar,
      notificationCount: "NEW", // “NEW” badge
    },
  ];

  // === SUPPORT LINKS ===
  const supportLinks: LinkItem[] = [
    {
      name: "Settings",
      href: "settings",
      icon: Settings,
    },
    {
      name: "Help Center",
      href: "help-center",
      icon: HelpCircle,
    },
  ];

  return (
    <>
      {/* Sidebar */}
      <div className="hidden w-[300px] flex-col border-r bg-white p-4 md:flex">
        {/* Logo / Title */}
        <div className="flex items-center gap-2 py-3">
          <div className="flex h-8 w-8 items-center justify-center rounded bg-green-600 text-white">
            <BookOpen className="h-4 w-4" />
          </div>
          <span className="text-xl font-bold text-green-600">ExamPro</span>
          <button className="ml-auto rounded p-1 hover:bg-gray-100">
            <Menu className="h-4 w-4" />
          </button>
        </div>

        {/* MAIN SECTION */}
        <div className="mt-6">
          <p className="mb-2 text-xs font-semibold text-gray-400 uppercase tracking-wide">
            MAIN
          </p>
          <nav className="grid gap-1">
            {mainLinks.map((item) => {
              const Icon = item.icon;
              const isActive = item.isActive ?? false;

              return (
                <Link
                  key={item.name}
                  href={`/teacher/${univId}/${item.href}`}
                  className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium ${
                    isActive
                      ? "bg-green-50 text-green-600"
                      : "text-gray-600 hover:bg-gray-100"
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {item.name}
                  {item.notificationCount !== undefined && (
                    <span
                      className={`ml-auto flex h-5 w-5 items-center justify-center rounded-full px-1 text-xs font-medium ${
                        typeof item.notificationCount === "number"
                          ? "bg-red-100 text-red-600"
                          : "bg-green-100 text-green-600"
                      }`}
                    >
                      {item.notificationCount}
                    </span>
                  )}
                </Link>
              );
            })}
          </nav>
        </div>

        {/* CONTENT SECTION */}
        <div className="mt-6">
          <p className="mb-2 text-xs font-semibold text-gray-400 uppercase tracking-wide">
            CONTENT
          </p>
          <nav className="grid gap-1">
            {contentLinks.map((item) => {
              const Icon = item.icon;
              // Content links are never “active” by default, so no green styling
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center justify-between rounded-md px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100"
                >
                  <div className="flex items-center gap-3">
                    <Icon className="h-4 w-4" />
                    {item.name}
                  </div>
                  {item.notificationCount !== undefined && (
                    <span className="rounded bg-green-100 px-1.5 py-0.5 text-[10px] font-medium text-green-600">
                      {item.notificationCount}
                    </span>
                  )}
                </Link>
              );
            })}
          </nav>
        </div>

        {/* SUPPORT SECTION */}
        <div className="mt-6">
          <p className="mb-2 text-xs font-semibold text-gray-400 uppercase tracking-wide">
            SUPPORT
          </p>
          <nav className="grid gap-1">
            {supportLinks.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100"
                >
                  <Icon className="h-4 w-4" />
                  {item.name}
                </Link>
              );
            })}
          </nav>
        </div>

        {/* FOOTER / USER PROFILE */}
        <div className="mt-auto pt-4">
          <div className="flex items-center gap-3 rounded-md border p-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-500 text-white">
              <span className="font-medium">DR</span>
            </div>
            <div>
              <p className="text-sm font-medium">Dr. Sarah Wilson</p>
              <p className="text-xs text-gray-500">Mathematics Dept.</p>
            </div>
            <button className="ml-auto rounded p-1 hover:bg-gray-100">
              <ChevronDown className="h-4 w-4" />
            </button>
          </div>
          <p className="mt-4 text-center text-xs text-gray-500">
            © 2024 ExamPro Platform
          </p>
        </div>
      </div>
    </>
  );
};

export default SideBar;
