import { verifyStudentSession } from '@/app/lib/student-session'
import { redirect } from 'next/navigation'
import React from 'react'
import { start } from 'repl'
import UniversityAIProctor from './UniversityProcto'
import prisma from '@/app/lib/prisma'

type Props = {
    params:Promise<{univId:string,sessionId:string}>
}

const page = async ({params}: Props) => {
    const {univId, sessionId} = await params
    const session = await verifyStudentSession()
    if(!session) {
        redirect('/student/' + univId + '/login')
    }
    // Fetch the exam session data using the sessionId
     const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/student/exam-session/${session.id}/${sessionId}`)
    if (!response.ok) {
        throw new Error('Failed to fetch exam session data')
    }
    //check if the exam session is not completed
    const examSessionData = await response.json()
    const now = new Date()
    const startDate = new Date(examSessionData.startDate)
    startDate.setHours(examSessionData.startTime.split(':')[0], examSessionData.startTime.split(':')[1], 0, 0)


    const endDate = new Date(startDate.getTime() + examSessionData.duration * 60000)
    if (now > endDate) {
        console.log("Time's up! Exam has been automatically submitted.")
        redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/student/${univId}/take-exam/${sessionId}/details`)
    }
    const isCompleted = await prisma.studentExamSession.findUnique({
        where: { studentId_examSessionId: { studentId: session.id, examSessionId: sessionId } },
        select: { status: true }
    })

    if(isCompleted.status !== "IN_PROGRESS") {
        console.log("Exam has been automatically submitted.")
        redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/student/${univId}/take-exam/${sessionId}/details`)
    }
    

    const student = await prisma.student.findUnique({
        where: { id: session.id },
        select: { name: true }
    })
    if (!student) {
        redirect(`/student/${univId}/login`)
    }
    // If the exam session is ongoing, redirect to the exam page


  return (
    <UniversityAIProctor examData={examSessionData}  examId={sessionId} studentId={session.id} studentName={student.name} />
  )
}

export default page