import { NextResponse } from 'next/server';
import axios from 'axios';

export async function GET() {
  try {
    // Only show this in development mode
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Debug endpoint not available in production' },
        { status: 403 }
      );
    }

    const results = {
      timestamp: new Date().toISOString(),
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      }
    };

    // Test 1: Basic internet connectivity
    try {
      const start = Date.now();
      await axios.get('https://www.google.com', { timeout: 10000 });
      const duration = Date.now() - start;
      results.tests.push({
        name: 'Internet Connectivity',
        status: 'passed',
        duration: `${duration}ms`,
        details: 'Successfully connected to google.com'
      });
      results.summary.passed++;
    } catch (error: any) {
      results.tests.push({
        name: 'Internet Connectivity',
        status: 'failed',
        error: error.message,
        details: 'Failed to connect to google.com'
      });
      results.summary.failed++;
    }
    results.summary.total++;

    // Test 2: Flutterwave API connectivity
    try {
      const start = Date.now();
      await axios.get('https://api.flutterwave.com', { 
        timeout: 15000,
        validateStatus: () => true // Accept any status code
      });
      const duration = Date.now() - start;
      results.tests.push({
        name: 'Flutterwave API Connectivity',
        status: 'passed',
        duration: `${duration}ms`,
        details: 'Successfully connected to api.flutterwave.com'
      });
      results.summary.passed++;
    } catch (error: any) {
      results.tests.push({
        name: 'Flutterwave API Connectivity',
        status: 'failed',
        error: error.message,
        code: error.code,
        details: 'Failed to connect to api.flutterwave.com'
      });
      results.summary.failed++;
    }
    results.summary.total++;

    // Test 3: DNS Resolution
    try {
      const dns = require('dns').promises;
      const start = Date.now();
      const addresses = await dns.resolve4('api.flutterwave.com');
      const duration = Date.now() - start;
      results.tests.push({
        name: 'DNS Resolution',
        status: 'passed',
        duration: `${duration}ms`,
        details: `Resolved to: ${addresses.join(', ')}`
      });
      results.summary.passed++;
    } catch (error: any) {
      results.tests.push({
        name: 'DNS Resolution',
        status: 'failed',
        error: error.message,
        details: 'Failed to resolve api.flutterwave.com'
      });
      results.summary.failed++;
    }
    results.summary.total++;

    // Test 4: SSL/TLS connectivity
    try {
      const https = require('https');
      const start = Date.now();
      
      await new Promise((resolve, reject) => {
        const req = https.request('https://api.flutterwave.com', {
          method: 'HEAD',
          timeout: 10000,
        }, (res: any) => {
          resolve(res);
        });
        
        req.on('error', reject);
        req.on('timeout', () => reject(new Error('SSL connection timeout')));
        req.end();
      });
      
      const duration = Date.now() - start;
      results.tests.push({
        name: 'SSL/TLS Connection',
        status: 'passed',
        duration: `${duration}ms`,
        details: 'SSL handshake successful'
      });
      results.summary.passed++;
    } catch (error: any) {
      results.tests.push({
        name: 'SSL/TLS Connection',
        status: 'failed',
        error: error.message,
        details: 'SSL handshake failed'
      });
      results.summary.failed++;
    }
    results.summary.total++;

    // Test 5: Flutterwave API authentication
    if (process.env.FLUTTERWAVE_SECRET_KEY) {
      try {
        const start = Date.now();
        const response = await axios.get('https://api.flutterwave.com/v3/balances', {
          headers: {
            'Authorization': `Bearer ${process.env.FLUTTERWAVE_SECRET_KEY}`,
          },
          timeout: 15000,
        });
        const duration = Date.now() - start;
        results.tests.push({
          name: 'Flutterwave API Authentication',
          status: 'passed',
          duration: `${duration}ms`,
          details: 'API key authentication successful'
        });
        results.summary.passed++;
      } catch (error: any) {
        results.tests.push({
          name: 'Flutterwave API Authentication',
          status: 'failed',
          error: error.response?.data?.message || error.message,
          statusCode: error.response?.status,
          details: 'API key authentication failed'
        });
        results.summary.failed++;
      }
    } else {
      results.tests.push({
        name: 'Flutterwave API Authentication',
        status: 'skipped',
        details: 'FLUTTERWAVE_SECRET_KEY not configured'
      });
    }
    results.summary.total++;

    // Add recommendations based on results
    const recommendations = [];
    
    if (results.summary.failed > 0) {
      recommendations.push('Some network tests failed. Check the details below for specific issues.');
    }
    
    const failedTests = results.tests.filter(test => test.status === 'failed');
    
    if (failedTests.some(test => test.name === 'Internet Connectivity')) {
      recommendations.push('Check your internet connection and firewall settings.');
    }
    
    if (failedTests.some(test => test.name === 'Flutterwave API Connectivity')) {
      recommendations.push('Flutterwave API is not reachable. This could be due to firewall, proxy, or regional restrictions.');
    }
    
    if (failedTests.some(test => test.name === 'DNS Resolution')) {
      recommendations.push('DNS resolution failed. Try using a different DNS server (*******, *******).');
    }
    
    if (failedTests.some(test => test.name === 'SSL/TLS Connection')) {
      recommendations.push('SSL/TLS connection failed. Check your system time and SSL certificates.');
    }
    
    if (failedTests.some(test => test.name === 'Flutterwave API Authentication')) {
      recommendations.push('API authentication failed. Verify your Flutterwave API keys.');
    }

    if (recommendations.length === 0) {
      recommendations.push('All network tests passed! Your connection to Flutterwave should work properly.');
    }

    return NextResponse.json({
      status: 'completed',
      results,
      recommendations,
      troubleshooting: {
        commonSolutions: [
          'Restart your development server',
          'Check your firewall and antivirus settings',
          'Try using a VPN if regional restrictions apply',
          'Verify your internet connection is stable',
          'Check if your ISP blocks certain domains'
        ],
        networkCommands: [
          'ping api.flutterwave.com',
          'nslookup api.flutterwave.com',
          'curl -I https://api.flutterwave.com',
          'telnet api.flutterwave.com 443'
        ]
      }
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to run network tests', details: error.message },
      { status: 500 }
    );
  }
}
