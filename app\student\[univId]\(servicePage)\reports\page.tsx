"use client"

import { useState } from "react"
import {
  BookOpen,
  Download,
  Mail,
  Save,
  Bell,
  HelpCircle,
  BarChart3,
  Users,
  Clock,
  TrendingUp,
  Target,
  Loader2,
  X,
} from "lucide-react"

export default function ReportsPage() {
  const [reportType, setReportType] = useState("exam-summary")
  const [selectedCourses, setSelectedCourses] = useState<string[]>([])
  const [selectedExams, setSelectedExams] = useState<string[]>([])
  const [dateRange, setDateRange] = useState("last-30-days")
  const [includeCharts, setIncludeCharts] = useState(true)
  const [includeRawData, setIncludeRawData] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [showSaveTemplate, setShowSaveTemplate] = useState(false)

  const courses = ["Mathematics", "Physics", "Chemistry", "Biology", "Computer Science"]
  const exams = [
    "Advanced Mathematics Midterm",
    "Physics Quantum Quiz",
    "Chemistry Lab Assessment",
    "Biology Final",
    "Computer Science Algorithms",
  ]

  const reportTypes = [
    {
      id: "exam-summary",
      name: "Exam Summary Report",
      description: "Overview statistics including average score, median, highest/lowest, pass rate",
      icon: BarChart3,
    },
    {
      id: "item-analysis",
      name: "Item Analysis Report",
      description: "Difficulty Index, Discrimination Index, Option Analysis for MCQs",
      icon: Target,
    },
    {
      id: "course-performance",
      name: "Course Performance Report",
      description: "Aggregate performance across all exams in selected courses",
      icon: TrendingUp,
    },
    {
      id: "student-progress",
      name: "Student Progress Report",
      description: "Track individual student performance over multiple exams",
      icon: Users,
    },
    {
      id: "time-analytics",
      name: "Time Analytics Report",
      description: "Analysis of time spent per question and per exam",
      icon: Clock,
    },
  ]

  const savedTemplates = [
    { id: "1", name: "Weekly Math Performance", type: "course-performance", lastUsed: "2024-01-15" },
    { id: "2", name: "Physics Midterm Analysis", type: "item-analysis", lastUsed: "2024-01-10" },
    { id: "3", name: "Student Progress - Fall 2024", type: "student-progress", lastUsed: "2024-01-08" },
  ]

  const handleGenerateReport = async () => {
    setIsGenerating(true)
    // Simulate report generation
    await new Promise((resolve) => setTimeout(resolve, 3000))
    setIsGenerating(false)
    setShowPreview(true)
  }

  const handleCourseToggle = (course: string) => {
    setSelectedCourses((prev) => (prev.includes(course) ? prev.filter((c) => c !== course) : [...prev, course]))
  }

  const handleExamToggle = (exam: string) => {
    setSelectedExams((prev) => (prev.includes(exam) ? prev.filter((e) => e !== exam) : [...prev, exam]))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-600 text-white">
                  <BookOpen className="h-4 w-4" />
                </div>
                <span className="text-xl font-bold text-blue-600">ExamPro</span>
              </div>
              <div className="hidden md:block h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">Reports</h1>
            </div>
            <div className="flex items-center gap-4">
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <Bell className="h-5 w-5" />
              </button>
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <HelpCircle className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid gap-6 lg:grid-cols-3">
            {/* Report Configuration */}
            <div className="lg:col-span-2 space-y-6">
              {/* Report Type Selection */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Report Type</h2>
                <div className="grid gap-3 md:grid-cols-2">
                  {reportTypes.map((type) => {
                    const Icon = type.icon
                    return (
                      <label
                        key={type.id}
                        className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                          reportType === type.id
                            ? "border-blue-600 bg-blue-50"
                            : "border-gray-300 bg-white hover:bg-gray-50"
                        }`}
                      >
                        <input
                          type="radio"
                          name="reportType"
                          value={type.id}
                          checked={reportType === type.id}
                          onChange={(e) => setReportType(e.target.value)}
                          className="sr-only"
                        />
                        <div className="flex items-start">
                          <div className="flex-shrink-0">
                            <Icon className={`h-6 w-6 ${reportType === type.id ? "text-blue-600" : "text-gray-400"}`} />
                          </div>
                          <div className="ml-3">
                            <div
                              className={`text-sm font-medium ${reportType === type.id ? "text-blue-900" : "text-gray-900"}`}
                            >
                              {type.name}
                            </div>
                            <div className={`text-sm ${reportType === type.id ? "text-blue-700" : "text-gray-500"}`}>
                              {type.description}
                            </div>
                          </div>
                        </div>
                      </label>
                    )
                  })}
                </div>
              </div>

              {/* Parameters */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Parameters</h2>
                <div className="space-y-6">
                  {/* Date Range */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                    <select
                      value={dateRange}
                      onChange={(e) => setDateRange(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="last-7-days">Last 7 Days</option>
                      <option value="last-30-days">Last 30 Days</option>
                      <option value="last-90-days">Last 90 Days</option>
                      <option value="current-semester">Current Semester</option>
                      <option value="custom">Custom Range</option>
                    </select>
                  </div>

                  {/* Course Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Courses ({selectedCourses.length} selected)
                    </label>
                    <div className="border border-gray-300 rounded-md p-3 max-h-32 overflow-y-auto">
                      {courses.map((course) => (
                        <label key={course} className="flex items-center py-1">
                          <input
                            type="checkbox"
                            checked={selectedCourses.includes(course)}
                            onChange={() => handleCourseToggle(course)}
                            className="h-4 w-4 text-blue-600 rounded"
                          />
                          <span className="ml-2 text-sm text-gray-700">{course}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Exam Selection */}
                  {(reportType === "exam-summary" || reportType === "item-analysis") && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Specific Exams ({selectedExams.length} selected)
                      </label>
                      <div className="border border-gray-300 rounded-md p-3 max-h-32 overflow-y-auto">
                        {exams.map((exam) => (
                          <label key={exam} className="flex items-center py-1">
                            <input
                              type="checkbox"
                              checked={selectedExams.includes(exam)}
                              onChange={() => handleExamToggle(exam)}
                              className="h-4 w-4 text-blue-600 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">{exam}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Report Options */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Report Options</label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={includeCharts}
                          onChange={(e) => setIncludeCharts(e.target.checked)}
                          className="h-4 w-4 text-blue-600 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Include charts and visualizations</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={includeRawData}
                          onChange={(e) => setIncludeRawData(e.target.checked)}
                          className="h-4 w-4 text-blue-600 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Include raw data tables</span>
                      </label>
                      {reportType === "item-analysis" && (
                        <label className="flex items-center">
                          <input type="checkbox" className="h-4 w-4 text-blue-600 rounded" />
                          <span className="ml-2 text-sm text-gray-700">Skip essay questions</span>
                        </label>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Generate Report */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Generate Report</h3>
                    <p className="text-sm text-gray-600">
                      Click generate to create your report with the selected parameters
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setShowSaveTemplate(true)}
                      className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      <Save className="h-4 w-4" />
                      Save Template
                    </button>
                    <button
                      onClick={handleGenerateReport}
                      disabled={isGenerating}
                      className="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      {isGenerating ? <Loader2 className="h-4 w-4 animate-spin" /> : <BarChart3 className="h-4 w-4" />}
                      {isGenerating ? "Generating..." : "Generate Report"}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Saved Templates */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Saved Templates</h3>
                {savedTemplates.length > 0 ? (
                  <div className="space-y-3">
                    {savedTemplates.map((template) => (
                      <div key={template.id} className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900">{template.name}</div>
                            <div className="text-sm text-gray-600 capitalize">{template.type.replace("-", " ")}</div>
                            <div className="text-xs text-gray-500">
                              Last used: {new Date(template.lastUsed).toLocaleDateString()}
                            </div>
                          </div>
                          <button className="text-blue-600 hover:text-blue-700 text-sm">Load</button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No saved templates</p>
                )}
              </div>

              {/* Quick Stats */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Reports Generated</span>
                    <span className="font-medium text-gray-900">47</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">This Month</span>
                    <span className="font-medium text-gray-900">12</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Most Used Type</span>
                    <span className="font-medium text-gray-900">Exam Summary</span>
                  </div>
                </div>
              </div>

              {/* Help */}
              <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Need Help?</h3>
                <p className="text-sm text-blue-700 mb-4">
                  Learn how to create effective reports and interpret analytics data.
                </p>
                <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">View Report Guide →</button>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Modals */}
      {showPreview && <ReportPreviewModal onClose={() => setShowPreview(false)} reportType={reportType} />}
      {showSaveTemplate && <SaveTemplateModal onClose={() => setShowSaveTemplate(false)} />}
    </div>
  )
}

function ReportPreviewModal({ onClose, reportType }: { onClose: () => void; reportType: string }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Report Preview</h2>
          <div className="flex items-center gap-2">
            <button className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
              <Download className="h-4 w-4" />
              Download PDF
            </button>
            <button className="inline-flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
              <Download className="h-4 w-4" />
              Excel
            </button>
            <button className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
              <Mail className="h-4 w-4" />
              Email
            </button>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <ReportPreviewContent reportType={reportType} />
        </div>
      </div>
    </div>
  )
}

function ReportPreviewContent({ reportType }: { reportType: string }) {
  return (
    <div className="space-y-8">
      {/* Report Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900">
          {reportType === "exam-summary" && "Exam Summary Report"}
          {reportType === "item-analysis" && "Item Analysis Report"}
          {reportType === "course-performance" && "Course Performance Report"}
          {reportType === "student-progress" && "Student Progress Report"}
          {reportType === "time-analytics" && "Time Analytics Report"}
        </h1>
        <p className="text-gray-600">Generated on {new Date().toLocaleDateString()}</p>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-4 gap-6">
        <div className="bg-blue-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">128</div>
          <div className="text-sm text-blue-700">Total Submissions</div>
        </div>
        <div className="bg-green-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-600">82.3%</div>
          <div className="text-sm text-green-700">Average Score</div>
        </div>
        <div className="bg-yellow-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-yellow-600">76.5%</div>
          <div className="text-sm text-yellow-700">Pass Rate</div>
        </div>
        <div className="bg-purple-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">47 min</div>
          <div className="text-sm text-purple-700">Avg Duration</div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-2 gap-6">
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Score Distribution</h3>
          <div className="h-64 flex items-end justify-around">
            {[
              { grade: "A", count: 25, height: 80 },
              { grade: "B", count: 45, height: 100 },
              { grade: "C", count: 35, height: 70 },
              { grade: "D", count: 15, height: 40 },
              { grade: "F", count: 8, height: 25 },
            ].map((item) => (
              <div key={item.grade} className="flex flex-col items-center">
                <div className="w-12 bg-blue-500 rounded-t" style={{ height: `${item.height}px` }}></div>
                <div className="text-sm font-medium mt-2">{item.grade}</div>
                <div className="text-xs text-gray-600">{item.count}</div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Trend</h3>
          <div className="h-64 flex items-end justify-around">
            {[
              { month: "Sep", score: 78 },
              { month: "Oct", score: 82 },
              { month: "Nov", score: 79 },
              { month: "Dec", score: 85 },
              { month: "Jan", score: 83 },
            ].map((item, index) => (
              <div key={item.month} className="flex flex-col items-center">
                <div className="w-8 bg-green-500 rounded-t" style={{ height: `${(item.score / 100) * 200}px` }}></div>
                <div className="text-sm font-medium mt-2">{item.month}</div>
                <div className="text-xs text-gray-600">{item.score}%</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Detailed Results</h3>
        <div className="overflow-x-auto">
          <table className="w-full border border-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="border-b border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-600">Exam</th>
                <th className="border-b border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-600">
                  Students
                </th>
                <th className="border-b border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-600">
                  Avg Score
                </th>
                <th className="border-b border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-600">
                  Pass Rate
                </th>
                <th className="border-b border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-600">
                  Difficulty
                </th>
              </tr>
            </thead>
            <tbody>
              {[
                { exam: "Mathematics Midterm", students: 45, avgScore: 82.5, passRate: 87, difficulty: "Medium" },
                { exam: "Physics Quiz", students: 38, avgScore: 78.2, passRate: 76, difficulty: "Hard" },
                { exam: "Chemistry Lab", students: 52, avgScore: 85.1, passRate: 92, difficulty: "Easy" },
                { exam: "Biology Final", students: 67, avgScore: 79.8, passRate: 82, difficulty: "Medium" },
              ].map((row, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="border-b border-gray-200 px-4 py-2 text-sm">{row.exam}</td>
                  <td className="border-b border-gray-200 px-4 py-2 text-sm">{row.students}</td>
                  <td className="border-b border-gray-200 px-4 py-2 text-sm">{row.avgScore}%</td>
                  <td className="border-b border-gray-200 px-4 py-2 text-sm">{row.passRate}%</td>
                  <td className="border-b border-gray-200 px-4 py-2 text-sm">{row.difficulty}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

function SaveTemplateModal({ onClose }: { onClose: () => void }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Save Report Template</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Template Name</label>
            <input
              type="text"
              placeholder="e.g., Weekly Math Performance"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Description (optional)</label>
            <textarea
              rows={3}
              placeholder="Describe what this template is used for..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button type="submit" className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Save Template
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
