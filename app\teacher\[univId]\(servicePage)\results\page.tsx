import React from 'react'
import ResultsPage from './ResultDisplay';
import { verifyInstructorSession } from '@/app/lib/instructor-session';
import { redirect } from 'next/navigation';

type Props = {}

const page = async (props: Props) => {
  const instructor = await verifyInstructorSession()
  if(!instructor) redirect('/')
  const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/teacher/${instructor.id}/result`);
  const data = await res.json();
  if(!res.ok) {

    return <div className="">{data.error}</div>
  };

  return (
    <ResultsPage p_results={data.results} />
  )
}

export default page