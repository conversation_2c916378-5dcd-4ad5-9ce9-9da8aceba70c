"use client"

import { useState } from "react"
import Link from "next/link"
import {
  BookOpen,
  Search,
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Users,
  Trash2,
  Calendar,
  Clock,
  FileText,
  ChevronDown,
  Play,
  CheckCircle,
  Bell,
  HelpCircle,
} from "lucide-react"

// Mock data for exams
const mockExams = [
  {
    id: 1,
    title: "Advanced Mathematics Midterm",
    status: "scheduled",
    startDate: "2024-01-25",
    endDate: "2024-01-25",
    duration: 120,
    totalQuestions: 25,
    assignedStudents: 45,
    subject: "Mathematics",
    createdAt: "2024-01-15",
  },
  {
    id: 2,
    title: "Physics Quantum Mechanics Quiz",
    status: "ongoing",
    startDate: "2024-01-20",
    endDate: "2024-01-20",
    duration: 60,
    totalQuestions: 15,
    assignedStudents: 38,
    subject: "Physics",
    createdAt: "2024-01-10",
  },
  {
    id: 3,
    title: "Chemistry Lab Assessment",
    status: "completed",
    startDate: "2024-01-15",
    endDate: "2024-01-15",
    duration: 90,
    totalQuestions: 20,
    assignedStudents: 52,
    subject: "Chemistry",
    createdAt: "2024-01-05",
  },
  {
    id: 4,
    title: "Biology Final Examination",
    status: "draft",
    startDate: "",
    endDate: "",
    duration: 180,
    totalQuestions: 40,
    assignedStudents: 0,
    subject: "Biology",
    createdAt: "2024-01-18",
  },
  {
    id: 5,
    title: "Computer Science Algorithms",
    status: "scheduled",
    startDate: "2024-02-01",
    endDate: "2024-02-01",
    duration: 150,
    totalQuestions: 30,
    assignedStudents: 28,
    subject: "Computer Science",
    createdAt: "2024-01-12",
  },
]

type ExamStatus = "draft" | "scheduled" | "ongoing" | "completed"

export default function MyExamsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<ExamStatus | "all">("all")
  const [subjectFilter, setSubjectFilter] = useState("all")
  const [viewMode, setViewMode] = useState<"table" | "cards">("cards")

  const subjects = ["Mathematics", "Physics", "Chemistry", "Biology", "Computer Science"]

  const filteredExams = mockExams.filter((exam) => {
    const matchesSearch = exam.title.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || exam.status === statusFilter
    const matchesSubject = subjectFilter === "all" || exam.subject === subjectFilter
    return matchesSearch && matchesStatus && matchesSubject
  })

  const getStatusBadge = (status: ExamStatus) => {
    const statusConfig = {
      draft: { color: "bg-gray-100 text-gray-800", icon: FileText },
      scheduled: { color: "bg-blue-100 text-blue-800", icon: Calendar },
      ongoing: { color: "bg-green-100 text-green-800", icon: Play },
      completed: { color: "bg-purple-100 text-purple-800", icon: CheckCircle },
    }

    const config = statusConfig[status]
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-600 text-white">
                  <BookOpen className="h-4 w-4" />
                </div>
                <span className="text-xl font-bold text-blue-600">ExamPro</span>
              </div>
              <div className="hidden md:block h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">My Exams</h1>
            </div>
            <div className="flex items-center gap-4">
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <Bell className="h-5 w-5" />
              </button>
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <HelpCircle className="h-5 w-5" />
              </button>
              <Link
                href="/create-exam"
                className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                <Plus className="h-4 w-4" />
                Create New Exam
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* Filters and Search */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search exams by title..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex gap-3">
                {/* Status Filter */}
                <div className="relative">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value as ExamStatus | "all")}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="draft">Draft</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="ongoing">Ongoing</option>
                    <option value="completed">Completed</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Subject Filter */}
                <div className="relative">
                  <select
                    value={subjectFilter}
                    onChange={(e) => setSubjectFilter(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Subjects</option>
                    {subjects.map((subject) => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* View Mode Toggle */}
                <div className="flex border border-gray-300 rounded-md">
                  <button
                    onClick={() => setViewMode("cards")}
                    className={`px-3 py-2 text-sm font-medium rounded-l-md transition-colors ${
                      viewMode === "cards" ? "bg-blue-600 text-white" : "bg-white text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    Cards
                  </button>
                  <button
                    onClick={() => setViewMode("table")}
                    className={`px-3 py-2 text-sm font-medium rounded-r-md transition-colors ${
                      viewMode === "table" ? "bg-blue-600 text-white" : "bg-white text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    Table
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Results Summary */}
          <div className="mb-6">
            <p className="text-sm text-gray-600">
              Showing {filteredExams.length} of {mockExams.length} exams
            </p>
          </div>

          {/* Exams Display */}
          {viewMode === "cards" ? (
            <ExamCards exams={filteredExams} getStatusBadge={getStatusBadge} />
          ) : (
            <ExamTable exams={filteredExams} getStatusBadge={getStatusBadge} />
          )}
        </div>
      </main>
    </div>
  )
}

function ExamCards({ exams, getStatusBadge }: { exams: any[]; getStatusBadge: (status: ExamStatus) => JSX.Element }) {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {exams.map((exam) => (
        <ExamCard key={exam.id} exam={exam} getStatusBadge={getStatusBadge} />
      ))}
    </div>
  )
}

function ExamCard({ exam, getStatusBadge }: { exam: any; getStatusBadge: (status: ExamStatus) => JSX.Element }) {
  const [showDropdown, setShowDropdown] = useState(false)

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{exam.title}</h3>
            <p className="text-sm text-gray-600">{exam.subject}</p>
          </div>
          <div className="relative">
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="p-1 rounded hover:bg-gray-100 transition-colors"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-500" />
            </button>
            {showDropdown && (
              <div className="absolute right-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <div className="py-1">
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <Eye className="h-4 w-4" />
                    View Exam
                  </button>
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <Edit className="h-4 w-4" />
                    Edit Exam
                  </button>
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <FileText className="h-4 w-4" />
                    Manage Questions
                  </button>
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <Users className="h-4 w-4" />
                    Assign Students
                  </button>
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                    <Trash2 className="h-4 w-4" />
                    Delete Exam
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Status */}
        <div className="mb-4">{getStatusBadge(exam.status)}</div>

        {/* Details */}
        <div className="space-y-3">
          {exam.startDate && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>
                {new Date(exam.startDate).toLocaleDateString()} - {new Date(exam.endDate).toLocaleDateString()}
              </span>
            </div>
          )}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Clock className="h-4 w-4" />
            <span>{exam.duration} minutes</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <FileText className="h-4 w-4" />
            <span>{exam.totalQuestions} questions</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Users className="h-4 w-4" />
            <span>{exam.assignedStudents} students</span>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-6 flex gap-2">
          <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium">
            View Details
          </button>
          <button className="bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors text-sm font-medium">
            Edit
          </button>
        </div>
      </div>
    </div>
  )
}

function ExamTable({ exams, getStatusBadge }: { exams: any[]; getStatusBadge: (status: ExamStatus) => JSX.Element }) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Exam Title
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">Status</th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Start Date
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                End Date
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Duration
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Questions
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Students
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {exams.map((exam) => (
              <ExamTableRow key={exam.id} exam={exam} getStatusBadge={getStatusBadge} />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

function ExamTableRow({ exam, getStatusBadge }: { exam: any; getStatusBadge: (status: ExamStatus) => JSX.Element }) {
  const [showDropdown, setShowDropdown] = useState(false)

  return (
    <tr className="hover:bg-gray-50">
      <td className="py-4 px-6">
        <div>
          <div className="text-sm font-medium text-gray-900">{exam.title}</div>
          <div className="text-sm text-gray-500">{exam.subject}</div>
        </div>
      </td>
      <td className="py-4 px-6">{getStatusBadge(exam.status)}</td>
      <td className="py-4 px-6 text-sm text-gray-900">
        {exam.startDate ? new Date(exam.startDate).toLocaleDateString() : "-"}
      </td>
      <td className="py-4 px-6 text-sm text-gray-900">
        {exam.endDate ? new Date(exam.endDate).toLocaleDateString() : "-"}
      </td>
      <td className="py-4 px-6 text-sm text-gray-900">{exam.duration} min</td>
      <td className="py-4 px-6 text-sm text-gray-900">{exam.totalQuestions}</td>
      <td className="py-4 px-6 text-sm text-gray-900">{exam.assignedStudents}</td>
      <td className="py-4 px-6">
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            className="p-1 rounded hover:bg-gray-100 transition-colors"
          >
            <MoreHorizontal className="h-4 w-4 text-gray-500" />
          </button>
          {showDropdown && (
            <div className="absolute right-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
              <div className="py-1">
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                  <Eye className="h-4 w-4" />
                  View Exam
                </button>
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                  <Edit className="h-4 w-4" />
                  Edit Exam
                </button>
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                  <FileText className="h-4 w-4" />
                  Manage Questions
                </button>
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                  <Users className="h-4 w-4" />
                  Assign Students
                </button>
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                  <Trash2 className="h-4 w-4" />
                  Delete Exam
                </button>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  )
}
