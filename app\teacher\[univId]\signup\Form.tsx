"use client"

import type React from "react"
import { useEffect, useState } from "react"

import Link from "next/link"
import { Book<PERSON>pen, EyeOff, Mail, Lock, User, Building, ArrowRight, Check, Key, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import { redirect } from "next/navigation"

export default function SignupPage({univID,univName}:{univID: string,univName: string}) {
  const [currentStep, setCurrentStep] = useState<"token" | "signup">("token")
  const [token, setToken] = useState("")
  const [isValidating, setIsValidating] = useState(false)
  const [sessionChecked, setSessionChecked] = useState(false)
  const [sessionUniv, setSessionUniv] = useState<{id:string,name:string}|null>(null)
  const [isCreating, setIsCreating] = useState(false)

  useEffect(() => {
    async function checkSession() {
      const res = await fetch("/api/instructor/verify-session")
      const data = await res.json()
      if (data.success && data.user) {
        setSessionUniv({id: data.user.univId, name: univName})
        setCurrentStep("signup")
      }
      setSessionChecked(true)
    }
    checkSession()
  }, [univName])

  const handleTokenSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsValidating(true)
    const res = await fetch("/api/instructor/token-session", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ token })
    })
    const data = await res.json()
    if (data.success) {
      setSessionUniv({id: data.univId, name: data.univName})
      setCurrentStep("signup")
    } else {
      alert(data.error || "Invalid token. Please enter a valid instructor creation token.")
    }
    setIsValidating(false)
  }

  const handleBackToToken = () => {
    setCurrentStep("token")
    setToken("")
  }

  // Signup Form Submission
  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);
    const form = e.target as HTMLFormElement;
    const fullName = (form.elements.namedItem('fullName') as HTMLInputElement).value;
    const email = (form.elements.namedItem('email') as HTMLInputElement).value;
    const password = (form.elements.namedItem('password') as HTMLInputElement).value;
    // Add confirm password check if needed
    const res = await fetch('/api/instructor/signup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ fullName, email, password })
    });
    const data = await res.json();
    setIsCreating(false);
    if (data.success) {

      toast.success('Account created!');
      redirect('login')
      // Optionally redirect or update UI
    } else {
      toast.error(data.error || 'Signup failed.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex overflow-hidden">
      {/* Left Side - Forms Container */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-md w-full">
          {/* Forms Wrapper with Animation */}
          <div className="relative w-full">
            {/* Token Validation Form */}
            <div
              className={`w-full transition-transform duration-500 ease-in-out ${
                currentStep === "token" ? "translate-x-0" : "-translate-x-[200%]"
              } ${currentStep === "signup" ? "absolute top-0 left-0" : ""}`}
            >
              <div className="space-y-8">
                {/* Logo and Header */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-6">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-600 text-white">
                      <BookOpen className="h-6 w-6" />
                    </div>
                    <span className="text-2xl font-bold text-green-600">ExamPro</span>
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900">Instructor Registration</h2>
                  <p className="mt-2 text-sm text-gray-600">Enter your instructor creation token to get started</p>
                </div>

                {/* Token Form */}
                <form onSubmit={handleTokenSubmit} className="mt-8 space-y-6">
                  <div>
                    <label htmlFor="token" className="block text-sm font-medium text-gray-700 mb-1">
                      Creation Token
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Key className="h-4 w-4 text-gray-400" />
                      </div>
                      <input
                        id="token"
                        name="token"
                        type="text"
                        required
                        value={token}
                        onChange={(e) => setToken(e.target.value)}
                        className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                        placeholder="Enter your creation token"
                      />
                    </div>
                    <p className="mt-2 text-xs text-gray-500">
                      This token is provided by your institution's administrator
                    </p>
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isValidating || token.length < 6}
                      className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isValidating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Validating...
                        </>
                      ) : (
                        <>
                          Validate Token
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </>
                      )}
                    </button>
                  </div>

                  {/* Help Section */}
                  <div className="bg-green-50 rounded-md p-4">
                    <h3 className="text-sm font-medium text-green-800 mb-2">Need a creation token?</h3>
                    <p className="text-xs text-green-700 mb-3">
                      Contact your institution's ExamPro administrator to obtain an instructor creation token.
                    </p>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-xs text-green-700">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span>Tokens are unique per instructor</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-green-700">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span>Tokens expire after 30 days</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-green-700">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span>One-time use only</span>
                      </div>
                    </div>
                  </div>

                  {/* Back to Login */}
                  <div className="text-center">
                    <p className="text-sm text-gray-600">
                      Already have an account?{" "}
                      <Link href="/login" className="font-medium text-green-600 hover:text-green-500">
                        Sign in
                      </Link>
                    </p>
                  </div>
                </form>
              </div>
            </div>

            {/* Signup Form */}
            <div
              className={`w-full transition-transform duration-500 ease-in-out ${
                currentStep === "signup" ? "translate-x-0" : "translate-x-[200%]"
              } ${currentStep === "token" ? "absolute top-0 left-0" : ""}`}
            >
              <div className="space-y-8">
                {/* Back Button */}
                <div className="text-center">
                  <button
                    onClick={handleBackToToken}
                    className="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-500 mb-6"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Token
                  </button>
                </div>

                {/* Logo and Header */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-6">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-600 text-white">
                      <BookOpen className="h-6 w-6" />
                    </div>
                    <span className="text-2xl font-bold text-green-600">ExamPro</span>
                  </div>

                  {/* Token Success Indicator */}
                  <div className="bg-green-50 rounded-lg p-3 mb-6">
                    <div className="flex items-center justify-center gap-2">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-100">
                        <Check className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="text-sm font-medium text-green-800">Token validated successfully</span>
                    </div>
                  </div>

                  <h2 className="text-3xl font-bold text-gray-900">Create your account</h2>
                  <p className="mt-2 text-sm text-gray-600">Complete your instructor registration</p>
                </div>

                {/* Signup Form */}
                <form className="mt-8 space-y-6" onSubmit={handleSignup}>
                  <div className="space-y-4">
                    {/* Full Name Field */}
                    <div>
                      <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="fullName"
                          name="fullName"
                          type="text"
                          autoComplete="name"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Enter your full name"
                        />
                      </div>
                    </div>

                    {/* Email Field */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email address
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Mail className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="email"
                          name="email"
                          type="email"
                          autoComplete="email"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Enter your email"
                        />
                      </div>
                    </div>

                    {/* Institution Field */}
                    <div>
                      <label htmlFor="institution" className="block text-sm font-medium text-gray-700 mb-1">
                        Institution
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Building className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="institution"
                          name="institution"
                          type="text"
                          required
                          value={univName}
                          disabled
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Enter your institution name"
                        />
                      </div>
                    </div>

                    {/* Password Field */}
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        Password
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="password"
                          name="password"
                          type="password"
                          autoComplete="new-password"
                          required
                          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Create a password"
                        />
                        <button type="button" className="absolute inset-y-0 right-0 pr-3 flex items-center">
                          <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </button>
                      </div>
                    </div>

                    {/* Confirm Password Field */}
                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="confirmPassword"
                          name="confirmPassword"
                          type="password"
                          autoComplete="new-password"
                          required
                          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Confirm your password"
                        />
                        <button type="button" className="absolute inset-y-0 right-0 pr-3 flex items-center">
                          <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Password Requirements */}
                  <div className="bg-gray-50 rounded-md p-3">
                    <p className="text-xs font-medium text-gray-700 mb-2">Password must contain:</p>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>At least 8 characters</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>One uppercase letter</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>One number</span>
                      </div>
                    </div>
                  </div>

                  {/* Terms and Privacy */}
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="terms"
                        name="terms"
                        type="checkbox"
                        required
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="terms" className="text-gray-700">
                        I agree to the{" "}
                        <Link href="/terms" className="font-medium text-green-600 hover:text-green-500">
                          Terms of Service
                        </Link>{" "}
                        and{" "}
                        <Link href="/privacy" className="font-medium text-green-600 hover:text-green-500">
                          Privacy Policy
                        </Link>
                      </label>
                    </div>
                  </div>

                  {/* Create account button */}
                  <div>
                    <button
                    type="submit"
                    disabled={isCreating}
                      className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isCreating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Creating...
                        </>
                      ) : (
                        <>
                          Create account
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </>
                      )}
                    </button>
                  </div>

                  {/* Sign in link */}
                  <div className="text-center">
                    <p className="text-sm text-gray-600">
                      Already have an account?{" "}
                      <Link href={`/teacher/${univID}/login`} className="font-medium text-green-600 hover:text-green-500">
                        Sign in
                      </Link>
                    </p>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Image/Illustration */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0 bg-gradient-to-br from-green-600 to-green-600">
          <div className="flex items-center justify-center h-full p-12">
            <div className="text-center text-white">
              <div className="mb-8">
                <div className="inline-flex items-center justify-center w-24 h-24 bg-white/10 rounded-full mb-6">
                  {currentStep === "token" ? <Key className="h-12 w-12" /> : <BookOpen className="h-12 w-12" />}
                </div>
                <h2 className="text-3xl font-bold mb-4">
                  {currentStep === "token" ? "Secure Registration" : "Start Your Teaching Journey"}
                </h2>
                <p className="text-xl text-green-100 max-w-md">
                  {currentStep === "token"
                    ? "We use creation tokens to ensure only authorized instructors can register on our platform."
                    : "Create engaging exams, track student progress, and make grading effortless with our platform."}
                </p>
              </div>
              <div className="grid grid-cols-1 gap-4 max-w-sm mx-auto">
                {currentStep === "token" ? (
                  <>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Secure token validation</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Institution verified</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Quick setup process</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Admin approved access</span>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Free to get started</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Unlimited question bank</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Real-time analytics</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>24/7 support</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
