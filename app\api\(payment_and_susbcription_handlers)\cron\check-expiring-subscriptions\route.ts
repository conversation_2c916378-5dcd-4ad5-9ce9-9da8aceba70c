import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { sendRenewalReminderEmail } from '@/utils/mailer';

const prisma = new PrismaClient();

export async function GET() {
  try {
    console.log('Running subscription expiry check...');
    console.log('Note: Full subscription management is temporarily disabled until Prisma client is regenerated.');

    // For now, we'll check for payments that might need renewal reminders
    // This is a simplified version until the full subscription system is available

    const recentPayments = await prisma.payment.findMany({
      where: {
        subscription: 'completed',
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 100,
    });

    console.log(`Found ${recentPayments.length} recent completed payments`);

    // Send renewal reminders for payments that are approaching expiry
    let remindersSent = 0;
    const now = new Date();

    for (const payment of recentPayments) {
      try {
        // Calculate approximate expiry date based on subscription type
        const createdDate = new Date(payment.createdAt);
        let expiryDate: Date;

        if (payment.subscription === 'yearly') {
          expiryDate = new Date(createdDate);
          expiryDate.setFullYear(expiryDate.getFullYear() + 1);
        } else {
          // Default to monthly
          expiryDate = new Date(createdDate);
          expiryDate.setMonth(expiryDate.getMonth() + 1);
        }

        // Check if expiring in 7 days
        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

        if (daysUntilExpiry === 7 || daysUntilExpiry === 3 || daysUntilExpiry === 1) {
          // Send renewal reminder
          await sendRenewalReminderEmail(
            payment.email,
            payment.email.split('@')[0], // Use email prefix as name
            payment.subscription === 'yearly' ? 'Yearly Plan' : 'Monthly Plan',
            expiryDate,
            daysUntilExpiry
          );

          remindersSent++;
          console.log(`Sent renewal reminder to ${payment.email} (${daysUntilExpiry} days remaining)`);
        }
      } catch (error) {
        console.error('Failed to process payment for renewal reminder:', error);
      }
    }

    const summary = {
      recentPayments: recentPayments.length,
      remindersSent,
      note: 'Simplified renewal system active. Full subscription management available after Prisma client regeneration.',
    };

    console.log('Subscription check completed:', summary);

    return NextResponse.json({
      success: true,
      summary,
    });
  } catch (error: any) {
    console.error('Subscription expiry check failed:', error);
    return NextResponse.json(
      { success: false, error: 'Subscription check failed' },
      { status: 500 }
    );
  }
}
