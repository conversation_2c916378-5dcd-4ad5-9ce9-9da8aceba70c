import Link from 'next/link'
import { GraduationCap, ArrowLeft, HelpCircle, Mail, Phone, MessageCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const faqs = [
  {
    question: "How do I create an account?",
    answer: "Click 'Sign Up' and use your university email address. You'll need to verify your email before you can log in."
  },
  {
    question: "What browsers are supported?",
    answer: "We recommend using the latest versions of Chrome, Firefox, Safari, or Edge for the best experience."
  },
  {
    question: "How does proctoring work?",
    answer: "During proctored exams, your camera and microphone will be monitored. Make sure you're in a quiet, well-lit room with no distractions."
  },
  {
    question: "Can I take breaks during an exam?",
    answer: "This depends on the exam settings. Some exams allow pausing, while others must be completed in one session. Check the exam instructions."
  },
  {
    question: "What if I have technical issues during an exam?",
    answer: "Contact technical support immediately. Your exam session will be reviewed, and you may be given additional time or a retake opportunity."
  },
  {
    question: "How do I upload files for essay questions?",
    answer: "Click the upload button next to the question. Supported formats include PDF, DOC, DOCX, and TXT files up to 10MB."
  },
  {
    question: "When will I see my exam results?",
    answer: "Results are typically available within 24-48 hours after submission, depending on the grading method and instructor preferences."
  },
  {
    question: "Can I review my answers after submitting?",
    answer: "This depends on the exam settings. Some exams allow review, while others hide answers until results are released."
  }
]

export default function HelpPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-primary-100">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <GraduationCap className="h-8 w-8 text-primary-600" />
              <span className="text-xl font-bold text-secondary-900">
                Online Exam Platform
              </span>
            </Link>
            <Button variant="ghost" asChild>
              <Link href="/">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-secondary-900 mb-4">Help & Support</h1>
          <p className="text-xl text-secondary-600 max-w-2xl mx-auto">
            Find answers to common questions and get the support you need
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* FAQ Section */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <HelpCircle className="h-5 w-5 mr-2 text-primary-600" />
                  Frequently Asked Questions
                </CardTitle>
                <CardDescription>
                  Quick answers to the most common questions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {faqs.map((faq, index) => (
                    <div key={index} className="border-b border-secondary-200 pb-4 last:border-b-0">
                      <h3 className="font-semibold text-secondary-900 mb-2">
                        {faq.question}
                      </h3>
                      <p className="text-secondary-600">
                        {faq.answer}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Section */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Contact Support</CardTitle>
                <CardDescription>
                  Need more help? Get in touch with our support team
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-primary-600" />
                  <div>
                    <p className="font-medium text-secondary-900">Email Support</p>
                    <p className="text-sm text-secondary-600"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-primary-600" />
                  <div>
                    <p className="font-medium text-secondary-900">Phone Support</p>
                    <p className="text-sm text-secondary-600">(*************</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <MessageCircle className="h-5 w-5 text-primary-600" />
                  <div>
                    <p className="font-medium text-secondary-900">Live Chat</p>
                    <p className="text-sm text-secondary-600">Available 9 AM - 5 PM</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Requirements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div>
                    <p className="font-medium text-secondary-900">Browsers:</p>
                    <p className="text-secondary-600">Chrome 90+, Firefox 88+, Safari 14+, Edge 90+</p>
                  </div>
                  <div>
                    <p className="font-medium text-secondary-900">Internet:</p>
                    <p className="text-secondary-600">Stable broadband connection (5+ Mbps)</p>
                  </div>
                  <div>
                    <p className="font-medium text-secondary-900">Hardware:</p>
                    <p className="text-secondary-600">Webcam and microphone for proctored exams</p>
                  </div>
                  <div>
                    <p className="font-medium text-secondary-900">Operating System:</p>
                    <p className="text-secondary-600">Windows 10+, macOS 10.15+, or Linux</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Links</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Link href="/login" className="block text-primary-600 hover:text-primary-700 hover:underline">
                    Student Login
                  </Link>
                  <Link href="/signup" className="block text-primary-600 hover:text-primary-700 hover:underline">
                    Create Account
                  </Link>
                  <Link href="/forgot-password" className="block text-primary-600 hover:text-primary-700 hover:underline">
                    Reset Password
                  </Link>
                  <Link href="/terms" className="block text-primary-600 hover:text-primary-700 hover:underline">
                    Terms of Service
                  </Link>
                  <Link href="/privacy" className="block text-primary-600 hover:text-primary-700 hover:underline">
                    Privacy Policy
                  </Link>
                  <Link href="/about" className="block text-primary-600 hover:text-primary-700 hover:underline">
                    About Us
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Emergency Contact */}
        <Card className="mt-8 bg-error/5 border-error/20">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-error mb-2">
                Emergency Technical Support
              </h3>
              <p className="text-secondary-600 mb-4">
                If you're experiencing technical issues during an active exam, contact us immediately:
              </p>
              <div className="flex justify-center space-x-6">
                <div className="text-center">
                  <p className="font-medium text-secondary-900">Emergency Hotline</p>
                  <p className="text-lg font-bold text-error">(555) 911-EXAM</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-secondary-900">Emergency Email</p>
                  <p className="text-lg font-bold text-error"><EMAIL></p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
