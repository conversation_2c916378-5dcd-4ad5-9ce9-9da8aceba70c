import { NextResponse } from "next/server"
import prisma from "@/app/lib/prisma"

// GET /api/teacher/[teacherId]/exams
export async function GET(
  req: Request,
  context: { params: Promise<{ teacherId: string }> }
) {
  try {
    const { teacherId } = await context.params
    // Fetch all exams for this teacher, including course and exam sessions
    const exams = await prisma.exam.findMany({
      where: { instructorId: teacherId },
      include: {
        course: { select: { name: true } },
        sessions: { select: { id: true, students: true } },
        enrollments: { select: { student: { select: { id: true, name: true } } } },
        questions: true,
      },
      orderBy: { createdAt: "desc" },
    })

    const now = new Date()
    const result = []
    for (const exam of exams) {
      // Check if exam should be ongoing
      const examDate = new Date(exam.startDate)
      const [h, m] = exam.startTime.split(":").map(Number)
      examDate.setHours(h, m, 0, 0)
      let status = "scheduled"
      let sessionCreated = false
      if (now >= examDate && now <= new Date(examDate.getTime() + exam.duration * 60000)) {
        // If no session exists, leave status as scheduled
        if (!exam.sessions.length) {
          // Do not create session, leave status as scheduled
        } else {
          // Set all sessions to active (ongoing)
          for (const session of exam.sessions) {
            await prisma.examSession.update({ where: { id: session.id }, data: { status: "active" } })
          }
          status = "ongoing"
        }
      } else if (now > new Date(examDate.getTime() + exam.duration * 60000)) {
        // Set all sessions to completed
        for (const session of exam.sessions) {
          await prisma.examSession.update({ where: { id: session.id }, data: { status: "completed" } })
        }
        status = "completed"
      } 
      // Assigned students: count unique students in all sessions
      let assignedStudents = 0
      if (exam.enrollments.length) {
        const studentIds = new Set()
        for (const session of exam.enrollments) {
           studentIds.add(session.student.id)
        }
        assignedStudents = studentIds.size
      }
      result.push({
        id: exam.id,
        title: exam.title,
        status,
        startDate: exam.startDate,
        endDate: exam.startDate,
        duration: exam.duration,
        totalQuestions: exam.questions.length,
        assignedStudents,
        subject: exam.course?.name || "",
        createdAt: exam.createdAt,
      })
    }
    return NextResponse.json({ exams: result })
  } catch (e: any) {
    return NextResponse.json({ error: "Failed to fetch exams", details: e.message }, { status: 500 })
  }
}
