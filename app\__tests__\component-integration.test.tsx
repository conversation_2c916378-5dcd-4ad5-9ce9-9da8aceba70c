import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import MonitoringDashboard from '../admin/monitoring/page';

// Mock fetch globally
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/admin/monitoring',
}));

describe('Component Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset fetch mock
    mockFetch.mockClear();
  });

  describe('MonitoringDashboard Component', () => {
    const mockMonitoringData = {
      health: {
        overall: 'healthy',
        timestamp: '2024-01-20T10:30:00.000Z',
        services: [
          {
            name: 'database',
            status: 'healthy',
            lastChecked: '2024-01-20T10:30:00.000Z',
            responseTime: 45,
            details: {
              userCount: 150,
              paymentCount: 89,
              connectionPool: 'active',
            },
          },
          {
            name: 'email',
            status: 'healthy',
            lastChecked: '2024-01-20T10:30:00.000Z',
            responseTime: 120,
            details: {
              totalSent: 45,
              totalFailed: 2,
              successRate: 95.7,
            },
          },
          {
            name: 'payment_gateway',
            status: 'degraded',
            lastChecked: '2024-01-20T10:30:00.000Z',
            responseTime: 6000,
            error: 'Slow response time',
            details: {
              provider: 'flutterwave',
              apiStatus: 'slow',
            },
          },
          {
            name: 'external_dependencies',
            status: 'healthy',
            lastChecked: '2024-01-20T10:30:00.000Z',
            responseTime: 150,
            details: {
              internetConnectivity: 'available',
              dnsResolution: 'working',
            },
          },
        ],
        uptime: 86400000,
        version: '1.0.0',
      },
      metrics: {
        uptime: 86400000,
        serviceFailures: {
          database: 0,
          email: 1,
          payment_gateway: 2,
          external_deps: 0,
        },
        alertConfig: {
          enabled: true,
          recipientCount: 2,
          thresholds: {
            responseTime: 5000,
            errorRate: 10,
            consecutiveFailures: 3,
          },
        },
      },
    };

    it('should render monitoring dashboard with healthy status', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockMonitoringData,
      } as Response);

      render(<MonitoringDashboard />);

      // Check loading state initially
      expect(screen.getByText('Loading monitoring data...')).toBeInTheDocument();

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('System Monitoring')).toBeInTheDocument();
      });

      // Check overall status
      expect(screen.getByText('System Status: HEALTHY')).toBeInTheDocument();
      expect(screen.getByText('Version 1.0.0')).toBeInTheDocument();
      expect(screen.getByText(/Uptime: 1d 0h 0m/)).toBeInTheDocument();

      // Check service cards
      expect(screen.getByText('Database')).toBeInTheDocument();
      expect(screen.getByText('Email')).toBeInTheDocument();
      expect(screen.getByText('Payment Gateway')).toBeInTheDocument();
      expect(screen.getByText('External Dependencies')).toBeInTheDocument();

      // Check service statuses
      const healthyStatuses = screen.getAllByText('HEALTHY');
      expect(healthyStatuses).toHaveLength(3); // database, email, external_dependencies

      const degradedStatus = screen.getByText('DEGRADED');
      expect(degradedStatus).toBeInTheDocument();

      // Check response times
      expect(screen.getByText('Response: 45ms')).toBeInTheDocument();
      expect(screen.getByText('Response: 120ms')).toBeInTheDocument();
      expect(screen.getByText('Response: 6000ms')).toBeInTheDocument();
      expect(screen.getByText('Response: 150ms')).toBeInTheDocument();

      // Check error message for degraded service
      expect(screen.getByText('Error: Slow response time')).toBeInTheDocument();
    });

    it('should render monitoring dashboard with unhealthy status', async () => {
      const unhealthyData = {
        ...mockMonitoringData,
        health: {
          ...mockMonitoringData.health,
          overall: 'unhealthy',
          services: [
            {
              name: 'database',
              status: 'unhealthy',
              lastChecked: '2024-01-20T10:30:00.000Z',
              responseTime: 0,
              error: 'Connection failed',
            },
            ...mockMonitoringData.health.services.slice(1),
          ],
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => unhealthyData,
      } as Response);

      render(<MonitoringDashboard />);

      await waitFor(() => {
        expect(screen.getByText('System Status: UNHEALTHY')).toBeInTheDocument();
      });

      expect(screen.getByText('UNHEALTHY')).toBeInTheDocument();
      expect(screen.getByText('Error: Connection failed')).toBeInTheDocument();
    });

    it('should handle API errors gracefully', async () => {
      mockFetch.mockRejectedValueOnce(new Error('API Error'));

      render(<MonitoringDashboard />);

      await waitFor(() => {
        expect(screen.getByText('API Error')).toBeInTheDocument();
      });

      expect(screen.getByText('Retry')).toBeInTheDocument();
    });

    it('should handle refresh functionality', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockMonitoringData,
      } as Response);

      render(<MonitoringDashboard />);

      await waitFor(() => {
        expect(screen.getByText('System Monitoring')).toBeInTheDocument();
      });

      // Click refresh button
      const refreshButton = screen.getByText('Refresh');
      fireEvent.click(refreshButton);

      // Should call fetch again
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledTimes(2);
      });
    });

    it('should display alert configuration correctly', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockMonitoringData,
      } as Response);

      render(<MonitoringDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Alert Configuration')).toBeInTheDocument();
      });

      expect(screen.getByText('Alerts Enabled:')).toBeInTheDocument();
      expect(screen.getByText('Yes')).toBeInTheDocument();
      expect(screen.getByText('Recipients:')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
      expect(screen.getByText('Response Time Threshold:')).toBeInTheDocument();
      expect(screen.getByText('5000ms')).toBeInTheDocument();
      expect(screen.getByText('Failure Threshold:')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('should display service failures correctly', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockMonitoringData,
      } as Response);

      render(<MonitoringDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Service Failures')).toBeInTheDocument();
      });

      expect(screen.getByText('Database:')).toBeInTheDocument();
      expect(screen.getByText('0 failures')).toBeInTheDocument();
      expect(screen.getByText('Email:')).toBeInTheDocument();
      expect(screen.getByText('1 failures')).toBeInTheDocument();
      expect(screen.getByText('Payment gateway:')).toBeInTheDocument();
      expect(screen.getByText('2 failures')).toBeInTheDocument();
    });

    it('should format uptime correctly', async () => {
      const dataWithDifferentUptime = {
        ...mockMonitoringData,
        health: {
          ...mockMonitoringData.health,
          uptime: 3661000, // 1 hour, 1 minute, 1 second
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => dataWithDifferentUptime,
      } as Response);

      render(<MonitoringDashboard />);

      await waitFor(() => {
        expect(screen.getByText(/Uptime: 1h 1m/)).toBeInTheDocument();
      });
    });

    it('should handle disabled alerts configuration', async () => {
      const dataWithDisabledAlerts = {
        ...mockMonitoringData,
        metrics: {
          ...mockMonitoringData.metrics,
          alertConfig: {
            ...mockMonitoringData.metrics.alertConfig,
            enabled: false,
          },
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => dataWithDisabledAlerts,
      } as Response);

      render(<MonitoringDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Alert Configuration')).toBeInTheDocument();
      });

      expect(screen.getByText('No')).toBeInTheDocument();
    });

    it('should handle empty service failures', async () => {
      const dataWithNoFailures = {
        ...mockMonitoringData,
        metrics: {
          ...mockMonitoringData.metrics,
          serviceFailures: {},
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => dataWithNoFailures,
      } as Response);

      render(<MonitoringDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Service Failures')).toBeInTheDocument();
      });

      expect(screen.getByText('No service failures recorded')).toBeInTheDocument();
    });

    it('should auto-refresh every 30 seconds', async () => {
      jest.useFakeTimers();

      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockMonitoringData,
      } as Response);

      render(<MonitoringDashboard />);

      // Initial load
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledTimes(1);
      });

      // Fast-forward 30 seconds
      jest.advanceTimersByTime(30000);

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledTimes(2);
      });

      jest.useRealTimers();
    });

    it('should display last updated time', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockMonitoringData,
      } as Response);

      render(<MonitoringDashboard />);

      await waitFor(() => {
        expect(screen.getByText(/Last updated:/)).toBeInTheDocument();
      });
    });

    it('should show service icons correctly', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockMonitoringData,
      } as Response);

      render(<MonitoringDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Database')).toBeInTheDocument();
      });

      // Check that service cards are rendered (icons are SVGs, harder to test directly)
      const serviceCards = screen.getAllByText(/Response: \d+ms/);
      expect(serviceCards).toHaveLength(4);
    });
  });
});
