import { EmailService } from '../email-service';
import nodemailer from 'nodemailer';

// Mock nodemailer
jest.mock('nodemailer');

describe('EmailService', () => {
  let mockTransporter: jest.Mocked<nodemailer.Transporter>;
  let mockCreateTransporter: jest.MockedFunction<typeof nodemailer.createTransporter>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock transporter
    mockTransporter = {
      sendMail: jest.fn(),
      verify: jest.fn(),
    } as any;
    
    // Mock nodemailer.createTransporter
    mockCreateTransporter = nodemailer.createTransporter as jest.MockedFunction<typeof nodemailer.createTransporter>;
    mockCreateTransporter.mockReturnValue(mockTransporter);

    // Mock environment variables
    process.env.SMTP_HOST = 'smtp.test.com';
    process.env.SMTP_PORT = '587';
    process.env.SMTP_USER = '<EMAIL>';
    process.env.SMTP_PASS = 'test-password';
    process.env.EMAIL_USER = '<EMAIL>';
    process.env.EMAIL_PASS = 'test-password';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('sendEmail', () => {
    it('should send email successfully', async () => {
      const mockResult = {
        messageId: 'test-message-id',
        accepted: ['<EMAIL>'],
        rejected: [],
        response: '250 OK',
      };

      mockTransporter.sendMail.mockResolvedValue(mockResult);

      const result = await EmailService.sendEmail(
        '<EMAIL>',
        'Test Subject',
        'Test message body'
      );

      expect(result).toEqual({
        success: true,
        messageId: 'test-message-id',
        response: '250 OK',
      });

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Test Subject',
        text: 'Test message body',
      });
    });

    it('should handle email sending errors', async () => {
      const error = new Error('SMTP connection failed');
      mockTransporter.sendMail.mockRejectedValue(error);

      const result = await EmailService.sendEmail(
        '<EMAIL>',
        'Test Subject',
        'Test message body'
      );

      expect(result).toEqual({
        success: false,
        error: 'SMTP connection failed',
      });
    });

    it('should send HTML email when provided', async () => {
      const mockResult = {
        messageId: 'test-message-id',
        accepted: ['<EMAIL>'],
        rejected: [],
        response: '250 OK',
      };

      mockTransporter.sendMail.mockResolvedValue(mockResult);

      await EmailService.sendEmail(
        '<EMAIL>',
        'Test Subject',
        'Test message body',
        '<h1>Test HTML</h1>'
      );

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Test Subject',
        text: 'Test message body',
        html: '<h1>Test HTML</h1>',
      });
    });
  });

  describe('sendAlertEmail', () => {
    it('should send critical alert email', async () => {
      const mockResult = {
        messageId: 'alert-message-id',
        accepted: ['<EMAIL>', '<EMAIL>'],
        rejected: [],
        response: '250 OK',
      };

      mockTransporter.sendMail.mockResolvedValue(mockResult);

      const alert = {
        severity: 'critical' as const,
        service: 'database',
        status: 'unhealthy' as const,
        error: 'Connection failed',
        responseTime: 0,
        timestamp: '2024-01-20T10:30:00.000Z',
        environment: 'production',
      };

      const result = await EmailService.sendAlertEmail(
        ['<EMAIL>', '<EMAIL>'],
        alert
      );

      expect(result).toEqual({
        success: true,
        messageId: 'alert-message-id',
        response: '250 OK',
      });

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>,<EMAIL>',
        subject: '🚨 CRITICAL ALERT: database Service Down',
        text: expect.stringContaining('CRITICAL system alert'),
        html: expect.stringContaining('CRITICAL ALERT'),
      });
    });

    it('should send warning alert email', async () => {
      const mockResult = {
        messageId: 'warning-message-id',
        accepted: ['<EMAIL>'],
        rejected: [],
        response: '250 OK',
      };

      mockTransporter.sendMail.mockResolvedValue(mockResult);

      const alert = {
        severity: 'warning' as const,
        service: 'email',
        status: 'degraded' as const,
        error: 'Slow response time',
        responseTime: 6000,
        timestamp: '2024-01-20T10:30:00.000Z',
        environment: 'production',
      };

      const result = await EmailService.sendAlertEmail(
        ['<EMAIL>'],
        alert
      );

      expect(result.success).toBe(true);

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: '⚠️ WARNING: email Service Degraded',
        text: expect.stringContaining('WARNING system alert'),
        html: expect.stringContaining('WARNING'),
      });
    });

    it('should include troubleshooting information for database alerts', async () => {
      mockTransporter.sendMail.mockResolvedValue({
        messageId: 'test-id',
        accepted: ['<EMAIL>'],
        rejected: [],
        response: '250 OK',
      });

      const alert = {
        severity: 'critical' as const,
        service: 'database',
        status: 'unhealthy' as const,
        error: 'Connection timeout',
        responseTime: 0,
        timestamp: '2024-01-20T10:30:00.000Z',
        environment: 'production',
      };

      await EmailService.sendAlertEmail(['<EMAIL>'], alert);

      const sentEmail = mockTransporter.sendMail.mock.calls[0][0];
      expect(sentEmail.html).toContain('Check database server status');
      expect(sentEmail.html).toContain('Verify connection pool settings');
      expect(sentEmail.html).toContain('Review database logs');
    });

    it('should include troubleshooting information for email alerts', async () => {
      mockTransporter.sendMail.mockResolvedValue({
        messageId: 'test-id',
        accepted: ['<EMAIL>'],
        rejected: [],
        response: '250 OK',
      });

      const alert = {
        severity: 'warning' as const,
        service: 'email',
        status: 'degraded' as const,
        error: 'SMTP timeout',
        responseTime: 8000,
        timestamp: '2024-01-20T10:30:00.000Z',
        environment: 'production',
      };

      await EmailService.sendAlertEmail(['<EMAIL>'], alert);

      const sentEmail = mockTransporter.sendMail.mock.calls[0][0];
      expect(sentEmail.html).toContain('Check SMTP server configuration');
      expect(sentEmail.html).toContain('Verify email credentials');
      expect(sentEmail.html).toContain('Test email connectivity');
    });

    it('should handle alert email sending errors', async () => {
      const error = new Error('Failed to send alert');
      mockTransporter.sendMail.mockRejectedValue(error);

      const alert = {
        severity: 'critical' as const,
        service: 'database',
        status: 'unhealthy' as const,
        error: 'Connection failed',
        responseTime: 0,
        timestamp: '2024-01-20T10:30:00.000Z',
        environment: 'production',
      };

      const result = await EmailService.sendAlertEmail(['<EMAIL>'], alert);

      expect(result).toEqual({
        success: false,
        error: 'Failed to send alert',
      });
    });
  });

  describe('getEmailStats', () => {
    it('should verify email configuration and return stats', async () => {
      mockTransporter.verify.mockResolvedValue(true);

      const stats = await EmailService.getEmailStats();

      expect(stats).toEqual({
        totalSent: 0,
        totalFailed: 0,
        successRate: 100,
      });

      expect(mockTransporter.verify).toHaveBeenCalledTimes(1);
    });

    it('should handle email verification errors', async () => {
      const error = new Error('SMTP verification failed');
      mockTransporter.verify.mockRejectedValue(error);

      await expect(EmailService.getEmailStats()).rejects.toThrow('SMTP verification failed');
    });
  });

  describe('Configuration', () => {
    it('should use SMTP configuration when available', () => {
      EmailService.sendEmail('<EMAIL>', 'Subject', 'Body');

      expect(mockCreateTransporter).toHaveBeenCalledWith({
        host: 'smtp.test.com',
        port: 587,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: 'test-password',
        },
      });
    });

    it('should fallback to EMAIL configuration when SMTP not available', () => {
      delete process.env.SMTP_HOST;
      delete process.env.SMTP_PORT;
      delete process.env.SMTP_USER;
      delete process.env.SMTP_PASS;

      EmailService.sendEmail('<EMAIL>', 'Subject', 'Body');

      expect(mockCreateTransporter).toHaveBeenCalledWith({
        host: undefined,
        port: 587,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: 'test-password',
        },
      });
    });

    it('should handle missing email configuration', () => {
      delete process.env.SMTP_HOST;
      delete process.env.SMTP_PORT;
      delete process.env.SMTP_USER;
      delete process.env.SMTP_PASS;
      delete process.env.EMAIL_USER;
      delete process.env.EMAIL_PASS;

      EmailService.sendEmail('<EMAIL>', 'Subject', 'Body');

      expect(mockCreateTransporter).toHaveBeenCalledWith({
        host: undefined,
        port: 587,
        secure: false,
        auth: {
          user: undefined,
          pass: undefined,
        },
      });
    });
  });
});
