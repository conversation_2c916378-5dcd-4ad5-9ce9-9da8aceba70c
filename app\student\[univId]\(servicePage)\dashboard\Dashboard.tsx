"use client";

import React, { useState } from "react";
import {
  ClockIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlayIcon,
  ChartBarIcon,
  ClipboardListIcon,
  Loader,
} from "lucide-react";
import { enrollStudent } from "@/app/student/action";
import { toast } from "sonner";
import prisma from "@/app/lib/prisma";
import { verifyStudentSession } from "@/app/lib/student-session";

interface Exam {
  id: string;
  title: string;
  status: "upcoming" | "ongoing" | "completed";
  date: string;
  duration: number;
  totalQuestions: number;
  passingScore: number;
  enrolled: 'approve' | 'pending' | 'reject' | null;
  instructor: string;
  description: string;
  score?: number;
}

export default function Dashboard({exams,studentId,univId}:{exams:Exam[],studentId:string,univId:string}) {
  const [activeTab, setActiveTab] = useState("upcoming");
  const [filter, setFilter] = useState("all");
  const [loading,setLoading] = useState({
    continue:false
  })

  const [Exams,setExams] = useState<Exam[]>(exams)

 ;

 const handleContinue = async (examId: string) => {
  setLoading((prev) => ({ ...prev, continue: true }));

  const res = await fetch('/api/student/exam-start-session', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ studentId, examId }),
  });

  if (!res.ok) {
    console.error("Failed to create or fetch exam session");
    setLoading((prev) => ({ ...prev, continue: false }));
    return;
  }

  const data = await res.json();
  window.location.href = `/student/${univId}/take-exam/${data.sessionId}`;
  setLoading((prev) => ({ ...prev, continue: false }));
};

 const handleEnroll = async (examId: string,studentId:string) => {
  const res = await enrollStudent({ studentId, examId });
  if (res.success) {
    setExams((prev) =>
      prev.map((exam) =>
        exam.id === examId ? { ...exam, enrolled: 'pending' } : exam
      )
    );
  }
  else {
    toast.error( res.message || "Enrollment failed");
  }
}

  const filteredExams = Exams.filter((exam) => {
    if (filter === "enrolled" && !exam.enrolled) return false;
    return exam.status === activeTab;
  });

  const formatDate = (date: string) =>
    new Date(date).toLocaleString("en-US", {
      dateStyle: "medium",
      timeStyle: "short",
    });

  const getStatusBadge = (status: Exam["status"]) => {
    const base = "flex items-center gap-1 px-2 py-1 text-xs rounded-full font-medium";
    const iconClass = "w-4 h-4";

    switch (status) {
      case "upcoming":
        return (
          <span className={`${base} bg-blue-100 text-green-700`}>
            <ClockIcon className={iconClass} /> Upcoming
          </span>
        );
      case "ongoing":
        return (
          <span className={`${base} bg-yellow-100 text-yellow-800`}>
            <PlayIcon className={iconClass} /> Ongoing
          </span>
        );
      case "completed":
        return (
          <span className={`${base} bg-green-100 text-green-700`}>
            <CheckCircleIcon className={iconClass} /> Completed
          </span>
        );
    }
  };

  const getScoreBadge = (exam: Exam) => {
    if (exam.status !== "completed") return null;
    const passed = (exam.score ?? 0) >= exam.passingScore;
    return (
      <span className={`flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full ${passed ? "bg-green-200 text-green-700" : "bg-red-200 text-red-700"}`}>
        {passed ? <CheckCircleIcon className="w-4 h-4" /> : <XCircleIcon className="w-4 h-4" />} {exam.score}% {passed ? "Passed" : "Failed"}
      </span>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-blue-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Your Exams</h1>
          <div className="flex gap-2">
            {['all', 'enrolled'].map(f => (
              <button
                key={f}
                onClick={() => setFilter(f)}
                className={`px-4 py-2 text-sm rounded-full ${filter === f ? 'bg-white text-green-600 shadow' : 'text-gray-500 bg-gray-200'}`}
              >
                {f === 'all' ? 'All Exams' : 'My Enrollments'}
              </button>
            ))}
          </div>
        </div>

        <div className="flex gap-6 border-b mb-6">
          {['upcoming', 'ongoing', 'completed'].map(tab => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`pb-2 text-sm font-medium ${activeTab === tab ? 'border-b-2 border-green-600 text-green-600' : 'text-gray-500'}`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>

        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {filteredExams.length > 0 ? filteredExams.map(exam => (
            <div key={exam.id} className="bg-white p-5 rounded-lg shadow">
              <div className="mb-4">
                <div className="flex justify-between items-start">
                  <h2 className="text-lg font-semibold text-gray-800">{exam.title}</h2>
                  <div className="flex flex-col items-end gap-1">
                    {getStatusBadge(exam.status)}
                    {getScoreBadge(exam)}
                  </div>
                </div>
              </div>

              <div className="text-sm text-gray-600 space-y-2">
                <div className="flex items-center gap-2">
                  <CalendarIcon className="w-4 h-4 text-gray-400" /> {formatDate(exam.date)}
                </div>
                <div className="flex items-center gap-2">
                  <ClockIcon className="w-4 h-4 text-gray-400" /> {exam.duration} minutes
                </div>
                <div className="flex items-center gap-2">
                  <ClipboardListIcon className="w-4 h-4 text-gray-400" /> {exam.totalQuestions} questions
                </div>
                <div className="flex items-center gap-2">
                  <ChartBarIcon className="w-4 h-4 text-gray-400" /> Passing: {exam.passingScore}%
                </div>
              </div>

              <div className="mt-4 text-xs text-gray-500">
                Instructor: <span className="font-semibold text-gray-700">{exam.instructor}</span>
              </div>

              <div className="mt-4">
                {exam.status === 'upcoming' && !exam.enrolled && (
                  <button onClick={() => handleEnroll(exam.id,studentId)} className="bg-green-600 text-white text-sm py-2 px-4 rounded hover:bg-green-700">Enroll Now</button>
                )}
                {exam.status === 'upcoming' && exam.enrolled === 'approve' && (
                  <button className="bg-gray-100 text-gray-800 text-sm py-2 px-4 rounded">View Details</button>
                )}
                {exam.status === 'ongoing' && (!exam.enrolled) && (
                  <button onClick={() => handleEnroll(exam.id,studentId)} className="bg-blue-600 text-white text-sm py-2 px-4 rounded hover:bg-blue-700">Join Exam</button>
                )}
                {exam.status === 'ongoing' && exam.enrolled === 'pending' && (
                  <div className="flex flex-col items-center justify-center">
                    <button className="bg-gray-100 text-gray-800 text-sm py-2 px-4 rounded">Demand pending</button>
                    <p className="text-xs text-gray-500 mt-1">Waiting for approval</p>
                  </div>
                )}
                {exam.status === 'ongoing' && exam.enrolled === 'approve' && (
                  <button disabled={loading.continue} onClick={() => handleContinue(exam.id)} className="disabled:bg-green-300 bg-green-500 text-white text-sm py-2 px-4 rounded hover:bg-green-600">{loading.continue ? <Loader className={'mr-2 animate-spin duration-300'}/> : 'Continue Exam'}</button>
                )}
                {exam.status === 'completed'  && (
                  <button className="bg-indigo-500 text-white text-sm py-2 px-4 rounded hover:bg-indigo-600">View Results</button>
                )}
              </div>

              {exam.status === 'upcoming' && (
                <div className="mt-4">
                  <div className="h-2 rounded bg-blue-200 overflow-hidden">
                    <div className="w-3/4 h-full bg-green-600"></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">75% of students completed preparation</p>
                </div>
              )}
            </div>
          )) : (
            <p className="text-center text-gray-500 col-span-full py-12">No exams match your current filters</p>
          )}
        </div>
      </div>
    </div>
  );
}
