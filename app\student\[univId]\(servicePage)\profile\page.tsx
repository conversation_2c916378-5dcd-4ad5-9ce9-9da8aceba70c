"use client"
import React, { useState } from 'react';
import {
  User,
  Edit,
  X,
  Save,
  Lock,
  Mail,
  Phone,
  School,
  BookOpen,
  Calendar
} from 'lucide-react';

const Profile = () => {
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+237 6',
    institution: 'University of Example',
    department: 'Computer Science',
    year: '3rd Year',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setEditMode(false);
  };

  return (
    <div className="min-h-screen bg-slate-50 p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-slate-800 flex items-center gap-2">
          <User size={28} className="text-green-600" />
          Profile
        </h1>
        {!editMode && (
          <button
            onClick={() => setEditMode(true)}
            className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
          >
            <Edit size={16} /> Edit Profile
          </button>
        )}
      </div>

      <div className="bg-white text-black rounded-lg shadow mb-6">
        <div className="flex items-center p-6 border-b border-green-200">
          <div className="w-20 h-20 rounded-full bg-indigo-100 text-green-600 flex items-center justify-center font-bold text-xl">
            {formData.firstName.charAt(0)}{formData.lastName.charAt(0)}
          </div>
          <div className="ml-6">
            <h2 className="text-xl font-semibold text-green-800">
              {formData.firstName} {formData.lastName}
            </h2>
            <p className="text-sm text-green-500 flex items-center gap-1">
              <Mail size={16} /> {formData.email}
            </p>
            <p className="text-sm text-green-500 flex **:items-center gap-1">
              <School size={16} /> {formData.institution}
            </p>
          </div>
        </div>

        {editMode ? (
          <form onSubmit={handleSubmit} className="p-6">
            <div className="grid grid-cols-1 text-black md:grid-cols-2 gap-6 mb-6">
              {['firstName', 'lastName', 'email', 'phone', 'institution', 'department'].map(field => (
                <div key={field}>
                  <label htmlFor={field} className="block text-sm font-medium text-green-700 mb-1 capitalize">
                    {field.replace(/([A-Z])/g, ' $1')}
                  </label>
                  <input
                    type="text"
                    id={field}
                    name={field}
                    value={(formData as any)[field]}
                    onChange={handleChange}
                    className="w-full border border-green-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-400"
                  />
                </div>
              ))}
              <div>
                <label htmlFor="year" className="block text-sm font-medium text-green-700 mb-1">
                  Year
                </label>
                <select
                  id="year"
                  name="year"
                  value={formData.year}
                  onChange={handleChange}
                  className="w-full border border-green-300 text-black rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-400"
                >
                  <option>1st Year</option>
                  <option>2nd Year</option>
                  <option>3rd Year</option>
                  <option>4th Year</option>
                  <option>5th Year</option>
                  <option>Graduate</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end gap-4">
              <button
                type="button"
                onClick={() => setEditMode(false)}
                className="flex items-center gap-1 text-white px-4 py-2 bg-green-600 rounded-md hover:bg-green-700"
              >
                <X size={16} /> Cancel
              </button>
              <button
                type="submit"
                className="flex items-center gap-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                <Save size={16} /> Save Changes
              </button>
            </div>
          </form>
        ) : (
          <div className="p-6 grid grid-cols-1 text-black md:grid-cols-2 gap-6">
            {[{
              label: 'Email',
              value: formData.email,
              icon: <Mail size={16} />
            }, {
              label: 'Phone',
              value: formData.phone,
              icon: <Phone size={16} />
            }, {
              label: 'Institution',
              value: formData.institution,
              icon: <School size={16} />
            }, {
              label: 'Department',
              value: formData.department,
              icon: <BookOpen size={16} />
            }, {
              label: 'Year',
              value: formData.year,
              icon: <Calendar size={16} />
            }].map(({ label, value, icon }) => (
              <div key={label}>
                <h3 className="text-sm font-semibold text-slate-700">{label}</h3>
                <p className="text-slate-600 flex items-center gap-2 mt-1">{icon} {value}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      
        
      </div>
    
  );
};

export default Profile;