import { MonitoringService } from '../monitoring-service';
import { EmailService } from '../email-service';
import { PrismaClient } from '@prisma/client';

// Mock dependencies
jest.mock('../email-service');
jest.mock('@prisma/client');

// Mock fetch globally
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('MonitoringService', () => {
  let monitoringService: MonitoringService;
  let mockPrisma: jest.Mocked<PrismaClient>;
  let mockEmailService: jest.Mocked<typeof EmailService>;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Reset singleton instance
    (MonitoringService as any).instance = null;
    
    // Get fresh instance
    monitoringService = MonitoringService.getInstance();
    
    // Setup Prisma mock
    mockPrisma = new PrismaClient() as jest.Mocked<PrismaClient>;
    
    // Setup EmailService mock
    mockEmailService = EmailService as jest.Mocked<typeof EmailService>;
    
    // Mock environment variables
    process.env.MONITORING_ALERTS_ENABLED = 'true';
    process.env.MONITORING_EMAIL_RECIPIENTS = '<EMAIL>,<EMAIL>';
    process.env.MONITORING_WEBHOOK_URL = 'https://hooks.slack.com/test';
    process.env.MONITORING_RESPONSE_TIME_THRESHOLD = '5000';
    process.env.MONITORING_CONSECUTIVE_FAILURES_THRESHOLD = '3';
    process.env.FLUTTERWAVE_SECRET_KEY = 'test-secret-key';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = MonitoringService.getInstance();
      const instance2 = MonitoringService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Database Health Check', () => {
    it('should return healthy status when database is accessible', async () => {
      // Mock successful database query
      mockPrisma.$queryRaw = jest.fn().mockResolvedValue([{ result: 1 }]);
      mockPrisma.user = {
        count: jest.fn().mockResolvedValue(150),
      } as any;
      mockPrisma.payment = {
        count: jest.fn().mockResolvedValue(89),
      } as any;

      const result = await (monitoringService as any).checkDatabase();

      expect(result.status).toBe('healthy');
      expect(result.name).toBe('database');
      expect(result.responseTime).toBeGreaterThan(0);
      expect(result.details).toEqual({
        userCount: 150,
        paymentCount: 89,
        connectionPool: 'active',
      });
    });

    it('should return unhealthy status when database query fails', async () => {
      // Mock database error
      mockPrisma.$queryRaw = jest.fn().mockRejectedValue(new Error('Connection failed'));

      const result = await (monitoringService as any).checkDatabase();

      expect(result.status).toBe('unhealthy');
      expect(result.name).toBe('database');
      expect(result.error).toBe('Connection failed');
    });

    it('should return degraded status when database is slow', async () => {
      // Mock slow database response
      mockPrisma.$queryRaw = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve([{ result: 1 }]), 6000))
      );
      mockPrisma.user = { count: jest.fn().mockResolvedValue(150) } as any;
      mockPrisma.payment = { count: jest.fn().mockResolvedValue(89) } as any;

      const result = await (monitoringService as any).checkDatabase();

      expect(result.status).toBe('degraded');
      expect(result.responseTime).toBeGreaterThan(5000);
    });
  });

  describe('Email Service Health Check', () => {
    it('should return healthy status when email service is working', async () => {
      mockEmailService.getEmailStats = jest.fn().mockResolvedValue({
        totalSent: 45,
        totalFailed: 2,
        successRate: 95.7,
      });

      const result = await (monitoringService as any).checkEmailService();

      expect(result.status).toBe('healthy');
      expect(result.name).toBe('email');
      expect(result.details).toEqual({
        totalSent: 45,
        totalFailed: 2,
        successRate: 95.7,
      });
    });

    it('should return not_configured when email credentials are missing', async () => {
      delete process.env.EMAIL_USER;
      delete process.env.EMAIL_PASS;

      const result = await (monitoringService as any).checkEmailService();

      expect(result.status).toBe('not_configured');
      expect(result.name).toBe('email');
    });

    it('should return unhealthy when email service fails', async () => {
      mockEmailService.getEmailStats = jest.fn().mockRejectedValue(new Error('SMTP connection failed'));

      const result = await (monitoringService as any).checkEmailService();

      expect(result.status).toBe('unhealthy');
      expect(result.error).toBe('SMTP connection failed');
    });
  });

  describe('Payment Gateway Health Check', () => {
    it('should return healthy status when Flutterwave API is accessible', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ status: 'success' }),
      } as Response);

      const result = await (monitoringService as any).checkPaymentGateway();

      expect(result.status).toBe('healthy');
      expect(result.name).toBe('payment_gateway');
      expect(result.details).toEqual({
        provider: 'flutterwave',
        apiStatus: 'operational',
      });
    });

    it('should return not_configured when API keys are missing', async () => {
      delete process.env.FLUTTERWAVE_SECRET_KEY;

      const result = await (monitoringService as any).checkPaymentGateway();

      expect(result.status).toBe('not_configured');
    });

    it('should return unhealthy when API request fails', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      } as Response);

      const result = await (monitoringService as any).checkPaymentGateway();

      expect(result.status).toBe('unhealthy');
      expect(result.error).toContain('HTTP 500');
    });
  });

  describe('External Dependencies Health Check', () => {
    it('should return healthy status when external services are accessible', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
      } as Response);

      const result = await (monitoringService as any).checkExternalDependencies();

      expect(result.status).toBe('healthy');
      expect(result.name).toBe('external_dependencies');
      expect(result.details).toEqual({
        internetConnectivity: 'available',
        dnsResolution: 'working',
      });
    });

    it('should return unhealthy when external services are not accessible', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await (monitoringService as any).checkExternalDependencies();

      expect(result.status).toBe('unhealthy');
      expect(result.error).toBe('Network error');
    });
  });

  describe('System Health Check', () => {
    it('should return overall healthy status when all services are healthy', async () => {
      // Mock all services as healthy
      mockPrisma.$queryRaw = jest.fn().mockResolvedValue([{ result: 1 }]);
      mockPrisma.user = { count: jest.fn().mockResolvedValue(150) } as any;
      mockPrisma.payment = { count: jest.fn().mockResolvedValue(89) } as any;
      
      mockEmailService.getEmailStats = jest.fn().mockResolvedValue({
        totalSent: 45,
        totalFailed: 2,
        successRate: 95.7,
      });

      mockFetch
        .mockResolvedValueOnce({ ok: true, status: 200 } as Response) // Flutterwave
        .mockResolvedValueOnce({ ok: true, status: 200 } as Response); // External deps

      const result = await monitoringService.checkSystemHealth();

      expect(result.overall).toBe('healthy');
      expect(result.services).toHaveLength(4);
      expect(result.services.every(s => s.status === 'healthy')).toBe(true);
      expect(result.uptime).toBeGreaterThan(0);
      expect(result.version).toBe('1.0.0');
    });

    it('should return degraded status when some services are degraded', async () => {
      // Mock database as slow (degraded)
      mockPrisma.$queryRaw = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve([{ result: 1 }]), 6000))
      );
      mockPrisma.user = { count: jest.fn().mockResolvedValue(150) } as any;
      mockPrisma.payment = { count: jest.fn().mockResolvedValue(89) } as any;

      // Mock other services as healthy
      mockEmailService.getEmailStats = jest.fn().mockResolvedValue({
        totalSent: 45,
        totalFailed: 2,
        successRate: 95.7,
      });

      mockFetch
        .mockResolvedValueOnce({ ok: true, status: 200 } as Response)
        .mockResolvedValueOnce({ ok: true, status: 200 } as Response);

      const result = await monitoringService.checkSystemHealth();

      expect(result.overall).toBe('degraded');
      expect(result.services.find(s => s.name === 'database')?.status).toBe('degraded');
    });

    it('should return unhealthy status when critical services fail', async () => {
      // Mock database as failed
      mockPrisma.$queryRaw = jest.fn().mockRejectedValue(new Error('Database down'));

      // Mock other services as healthy
      mockEmailService.getEmailStats = jest.fn().mockResolvedValue({
        totalSent: 45,
        totalFailed: 2,
        successRate: 95.7,
      });

      mockFetch
        .mockResolvedValueOnce({ ok: true, status: 200 } as Response)
        .mockResolvedValueOnce({ ok: true, status: 200 } as Response);

      const result = await monitoringService.checkSystemHealth();

      expect(result.overall).toBe('unhealthy');
      expect(result.services.find(s => s.name === 'database')?.status).toBe('unhealthy');
    });
  });

  describe('Alert Processing', () => {
    it('should process alerts when enabled', async () => {
      mockEmailService.sendAlertEmail = jest.fn().mockResolvedValue({ success: true });
      mockFetch.mockResolvedValueOnce({ ok: true } as Response);

      const alert = {
        severity: 'critical' as const,
        service: 'database',
        status: 'unhealthy' as const,
        error: 'Connection failed',
        responseTime: 0,
        timestamp: new Date().toISOString(),
        environment: 'test',
      };

      await (monitoringService as any).processAlert(alert);

      expect(mockEmailService.sendAlertEmail).toHaveBeenCalledWith(
        ['<EMAIL>', '<EMAIL>'],
        alert
      );
      expect(mockFetch).toHaveBeenCalledWith(
        'https://hooks.slack.com/test',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        })
      );
    });

    it('should not process alerts when disabled', async () => {
      process.env.MONITORING_ALERTS_ENABLED = 'false';
      mockEmailService.sendAlertEmail = jest.fn();

      const alert = {
        severity: 'critical' as const,
        service: 'database',
        status: 'unhealthy' as const,
        error: 'Connection failed',
        responseTime: 0,
        timestamp: new Date().toISOString(),
        environment: 'test',
      };

      await (monitoringService as any).processAlert(alert);

      expect(mockEmailService.sendAlertEmail).not.toHaveBeenCalled();
    });
  });

  describe('Metrics Collection', () => {
    it('should return comprehensive metrics', async () => {
      const metrics = await monitoringService.getMetrics();

      expect(metrics).toHaveProperty('uptime');
      expect(metrics).toHaveProperty('serviceFailures');
      expect(metrics).toHaveProperty('alertConfig');
      expect(metrics.alertConfig).toEqual({
        enabled: true,
        recipientCount: 2,
        thresholds: {
          responseTime: 5000,
          errorRate: 10,
          consecutiveFailures: 3,
        },
      });
    });
  });
});
