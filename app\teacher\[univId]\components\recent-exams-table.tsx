import { Calendar, Clock, Users, CheckCircle, XCircle, AlertCircle } from "lucide-react"

export function RecentExamsTable() {
  const exams = [
    {
      id: 1,
      name: "Mathematics Midterm",
      date: "2024-01-15",
      duration: "2 hours",
      students: 45,
      completed: 42,
      status: "completed",
      avgScore: 82.5,
    },
    {
      id: 2,
      name: "Physics Quiz #3",
      date: "2024-01-18",
      duration: "45 mins",
      students: 38,
      completed: 35,
      status: "grading",
      avgScore: null,
    },
    {
      id: 3,
      name: "Chemistry Lab Test",
      date: "2024-01-20",
      duration: "1.5 hours",
      students: 52,
      completed: 0,
      status: "scheduled",
      avgScore: null,
    },
    {
      id: 4,
      name: "Biology Final Review",
      date: "2024-01-12",
      duration: "3 hours",
      students: 67,
      completed: 67,
      status: "completed",
      avgScore: 78.2,
    },
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "grading":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case "scheduled":
        return <Clock className="h-4 w-4 text-blue-500" />
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"
    switch (status) {
      case "completed":
        return `${baseClasses} bg-green-100 text-green-800`
      case "grading":
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case "scheduled":
        return `${baseClasses} bg-blue-100 text-blue-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  return (
    <div className="overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
              <th className="pb-3">Exam Name</th>
              <th className="pb-3">Date</th>
              <th className="pb-3">Students</th>
              <th className="pb-3">Status</th>
              <th className="pb-3">Avg Score</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {exams.map((exam) => (
              <tr key={exam.id} className="hover:bg-gray-50">
                <td className="py-4">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{exam.name}</p>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      {exam.duration}
                    </div>
                  </div>
                </td>
                <td className="py-4">
                  <div className="flex items-center gap-1 text-sm text-gray-900">
                    <Calendar className="h-3 w-3 text-gray-400" />
                    {new Date(exam.date).toLocaleDateString()}
                  </div>
                </td>
                <td className="py-4">
                  <div className="flex items-center gap-1 text-sm text-gray-900">
                    <Users className="h-3 w-3 text-gray-400" />
                    {exam.completed}/{exam.students}
                  </div>
                </td>
                <td className="py-4">
                  <span className={getStatusBadge(exam.status)}>
                    {getStatusIcon(exam.status)}
                    <span className="ml-1 capitalize">{exam.status}</span>
                  </span>
                </td>
                <td className="py-4">
                  {exam.avgScore ? (
                    <span className="text-sm font-medium text-gray-900">{exam.avgScore}%</span>
                  ) : (
                    <span className="text-sm text-gray-400">-</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
