# SmartOnline API Documentation

## API Overview

The SmartOnline API is organized around REST principles. All requests should be made over HTTPS in production, and all data is sent and received as JSON.

## Base URL

```
Production: https://api.smartonline.com
Development: http://localhost:3000/api
```

## Authentication

### JWT Authentication
All API requests must include a valid JWT token in the Authorization header:

```http
Authorization: Bearer <your_jwt_token>
```

## API Endpoints

### University Management

#### Create University
```http
POST /api/admin/create-university
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Example University",
  "domain": "example.edu",
  "location": "City, Country",
  "contactEmail": "<EMAIL>",
  "logo": "base64_encoded_image"
}

Response: 200 OK
{
  "id": "univ_123",
  "name": "Example University",
  "domain": "example.edu",
  "status": "pending"
}
```

#### Get University Dashboard
```http
GET /api/admin/{univId}/dashboard
Authorization: Bearer <token>

Response: 200 OK
{
  "statistics": {
    "totalStudents": 1500,
    "activeInstructors": 75,
    "departments": 12,
    "activeCourses": 150
  },
  "recentActivity": [...]
}
```

### Department Management

#### Create Department
```http
POST /api/admin/{univId}/departments
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Computer Science",
  "code": "CS",
  "description": "Department of Computer Science",
  "headOfDepartment": "Dr. John Doe"
}

Response: 200 OK
{
  "id": "dept_123",
  "name": "Computer Science",
  "code": "CS"
}
```

### Student Management

#### Create Student Account
```http
POST /api/auth/student/create
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securepassword",
  "universityId": "univ_123",
  "departmentId": "dept_123"
}

Response: 200 OK
{
  "id": "student_123",
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

#### Student Login
```http
POST /api/auth/student/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}

Response: 200 OK
{
  "token": "jwt_token_here",
  "user": {
    "id": "student_123",
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

### Payment Processing

#### Initialize Payment
```http
POST /api/payment
Content-Type: application/json
Authorization: Bearer <token>

{
  "amount": 100.00,
  "currency": "XAF",
  "method": "flutterwave",
  "planId": "plan_123"
}

Response: 200 OK
{
  "paymentId": "pay_123",
  "checkoutUrl": "https://payment-gateway.com/checkout",
  "transactionId": "trans_123"
}
```

#### Verify Payment
```http
POST /api/payment/verify
Content-Type: application/json
Authorization: Bearer <token>

{
  "transactionId": "trans_123"
}

Response: 200 OK
{
  "status": "success",
  "subscription": {
    "id": "sub_123",
    "status": "active",
    "expiryDate": "2024-12-31"
  }
}
```

### Subscription Management

#### Get Subscription Status
```http
GET /api/subscriptions/{id}
Authorization: Bearer <token>

Response: 200 OK
{
  "id": "sub_123",
  "status": "active",
  "plan": {
    "name": "Premium",
    "features": [...]
  },
  "expiryDate": "2024-12-31"
}
```

#### Update Subscription
```http
PUT /api/subscriptions/{id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "status": "cancelled",
  "reason": "Upgrade to different plan"
}

Response: 200 OK
{
  "id": "sub_123",
  "status": "cancelled",
  "cancelledAt": "2024-03-15T12:00:00Z"
}
```

### Token Management

#### Generate Instructor Tokens
```http
POST /api/admin/{univId}/tokens
Content-Type: application/json
Authorization: Bearer <token>

{
  "departmentId": "dept_123",
  "courseId": "course_123",
  "quantity": 50,
  "expirationDays": 30
}

Response: 200 OK
{
  "tokens": [
    {
      "code": "INS-123-ABC",
      "expirationDate": "2024-04-15T00:00:00Z"
    },
    ...
  ]
}
```

## Webhook Events

### Payment Webhooks

#### Flutterwave Webhook
```http
POST /api/webhooks/flutterwave
Content-Type: application/json

{
  "event": "charge.completed",
  "data": {
    "id": "trans_123",
    "status": "successful",
    "amount": 100.00
  }
}
```

#### MTN Mobile Money Webhook
```http
POST /api/webhooks/mtn
Content-Type: application/json

{
  "transactionId": "mtn_123",
  "status": "SUCCESSFUL",
  "amount": 100.00
}
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": {
      "field": "Additional error context"
    }
  }
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| 400  | Bad Request - Invalid parameters |
| 401  | Unauthorized - Invalid or missing token |
| 403  | Forbidden - Insufficient permissions |
| 404  | Not Found - Resource doesn't exist |
| 422  | Unprocessable Entity - Validation failed |
| 429  | Too Many Requests - Rate limit exceeded |
| 500  | Internal Server Error |

### Validation Errors
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "email": "Must be a valid email address",
      "password": "Must be at least 8 characters"
    }
  }
}
```

## Rate Limiting

- Anonymous IP: 100 requests per hour
- Authenticated user: 1000 requests per hour
- Webhook endpoints: 10000 requests per hour

Rate limit headers included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Pagination

### Request Parameters
```
?page=1&limit=20
```

### Response Format
```json
{
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 20,
    "pages": 5
  }
}
```

### System Monitoring

#### Health Check
```http
GET /api/status

Response: 200 OK
{
  "timestamp": "2024-01-20T10:30:00.000Z",
  "status": "healthy",
  "services": {
    "database": "healthy",
    "email": "healthy",
    "paymentGateways": {
      "flutterwave": "healthy"
    }
  },
  "statistics": {
    "totalUsers": 150,
    "totalSubscriptions": 45,
    "activeSubscriptions": 42,
    "totalPayments": 89,
    "completedPayments": 85
  },
  "monitoring": {
    "uptime": 86400000,
    "version": "1.0.0",
    "detailedServices": [
      {
        "name": "database",
        "status": "healthy",
        "lastChecked": "2024-01-20T10:30:00.000Z",
        "responseTime": 45,
        "details": {
          "userCount": 150,
          "paymentCount": 89,
          "connectionPool": "active"
        }
      }
    ]
  }
}
```

#### Enhanced Monitoring
```http
GET /api/monitoring

Response: 200 OK
{
  "health": {
    "overall": "healthy",
    "timestamp": "2024-01-20T10:30:00.000Z",
    "services": [
      {
        "name": "database",
        "status": "healthy",
        "lastChecked": "2024-01-20T10:30:00.000Z",
        "responseTime": 45,
        "details": {
          "userCount": 150,
          "paymentCount": 89,
          "connectionPool": "active"
        }
      },
      {
        "name": "email",
        "status": "healthy",
        "lastChecked": "2024-01-20T10:30:00.000Z",
        "responseTime": 120,
        "details": {
          "totalSent": 45,
          "totalFailed": 2,
          "successRate": 95.7
        }
      },
      {
        "name": "payment_gateway",
        "status": "healthy",
        "lastChecked": "2024-01-20T10:30:00.000Z",
        "responseTime": 200,
        "details": {
          "provider": "flutterwave",
          "apiStatus": "operational"
        }
      },
      {
        "name": "external_dependencies",
        "status": "healthy",
        "lastChecked": "2024-01-20T10:30:00.000Z",
        "responseTime": 150,
        "details": {
          "internetConnectivity": "available",
          "dnsResolution": "working"
        }
      }
    ],
    "uptime": 86400000,
    "version": "1.0.0"
  },
  "metrics": {
    "uptime": 86400000,
    "serviceFailures": {
      "database": 0,
      "email": 1,
      "payment_gateway": 0,
      "external_deps": 0
    },
    "alertConfig": {
      "enabled": true,
      "recipientCount": 2,
      "thresholds": {
        "responseTime": 5000,
        "errorRate": 10,
        "consecutiveFailures": 3
      }
    }
  }
}
```

#### Monitoring Actions
```http
POST /api/monitoring
Content-Type: application/json

{
  "action": "health-check"
}

Response: 200 OK
{
  "overall": "healthy",
  "timestamp": "2024-01-20T10:30:00.000Z",
  "services": [...],
  "uptime": 86400000,
  "version": "1.0.0"
}
```

```http
POST /api/monitoring
Content-Type: application/json

{
  "action": "test-alerts"
}

Response: 200 OK (Development only)
{
  "success": true,
  "message": "Test alert triggered",
  "alert": {
    "severity": "warning",
    "service": "test_service",
    "status": "degraded",
    "error": "This is a test alert",
    "responseTime": 5500,
    "timestamp": "2024-01-20T10:30:00.000Z",
    "environment": "development"
  }
}
```

#### Debug Endpoints (Development Only)

##### Environment Check
```http
GET /api/debug/env

Response: 200 OK (Development only)
{
  "status": "debug",
  "environment": {
    "NODE_ENV": "development",
    "NEXT_PUBLIC_BASE_URL": "http://localhost:3000",
    "FLUTTERWAVE_PUBLIC_KEY": "FLWPUBK_TEST-xxxxx...",
    "EMAIL_USER": "SET",
    "DATABASE_URL": "SET"
  },
  "issues": ["No issues detected"],
  "recommendations": [
    "Make sure all environment variables are set in your .env file",
    "Restart your development server after changing environment variables"
  ]
}
```

##### Email Service Debug
```http
GET /api/debug/email

Response: 200 OK (Development only)
{
  "emailStats": {
    "totalSent": 45,
    "totalFailed": 2,
    "successRate": 95.7
  },
  "recentPayments": [...],
  "troubleshooting": {
    "emailConfiguration": {
      "emailUser": "SET",
      "emailPass": "SET",
      "smtpConfigured": true
    }
  }
}
```

##### Network Connectivity Test
```http
GET /api/debug/network

Response: 200 OK (Development only)
{
  "status": "completed",
  "results": {
    "timestamp": "2024-01-20T10:30:00.000Z",
    "tests": [...],
    "summary": {
      "total": 5,
      "passed": 5,
      "failed": 0
    }
  },
  "recommendations": [...]
}
```

### Webhooks

#### Flutterwave Payment Webhook
```http
POST /api/webhooks/flutterwave
Content-Type: application/json
verif-hash: <webhook_signature>

{
  "event": "charge.completed",
  "data": {
    "tx_ref": "payment_123",
    "status": "successful",
    "amount": 50000,
    "currency": "XAF",
    "customer": {
      "email": "<EMAIL>"
    }
  }
}

Response: 200 OK
{
  "success": true
}
```

### Cron Jobs

#### Subscription Expiry Check
```http
GET /api/cron/check-expiring-subscriptions

Response: 200 OK
{
  "success": true,
  "summary": {
    "recentPayments": 25,
    "remindersSent": 3,
    "note": "Simplified renewal system active"
  }
}
```

## API Versioning

The API version is included in the URL:
```
/api/v1/endpoint
```

## Development Tools

### API Testing
```bash
# Using curl
curl -X POST https://api.smartonline.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Using Postman
Import the provided Postman collection: smartonline-api.postman_collection.json
```

### Sandbox Environment
```
https://sandbox.smartonline.com/api
```

Use test credentials:
- Email: <EMAIL>
- Password: test123
