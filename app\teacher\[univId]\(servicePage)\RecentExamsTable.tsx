import React from 'react';

type Exam = {
  id: string;
  title: string;
  createdAt: string;
};

interface RecentExamsTableProps {
  exams: Exam[];
}

const RecentExamsTable: React.FC<RecentExamsTableProps> = ({ exams }) => {
  return (
    <table className="min-w-full divide-y divide-gray-200">
      <thead>
        <tr>
          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        {exams.map((exam) => (
          <tr key={exam.id}>
            <td className="px-4 py-2 whitespace-nowrap">{exam.title}</td>
            <td className="px-4 py-2 whitespace-nowrap">{new Date(exam.createdAt).toLocaleDateString()}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default RecentExamsTable;
