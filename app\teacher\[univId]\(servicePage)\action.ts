'use server'

import prisma from "@/app/lib/prisma"

export async function updateEnrollmentStatus(id: string, action: 'approve' | 'reject') {
  try {
    const status = action === 'approve' ? 'approve' : 'reject'

    await prisma.examEnrollment.update({
      where: { id },
      data: { status },
    })

    return { success: true }
  } catch (error) {
    console.error('Failed to update enrollment status:', error)
    return { success: false, error: 'Update failed' }
  }
}