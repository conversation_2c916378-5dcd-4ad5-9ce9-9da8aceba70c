"use client"

import { useState, useEffect } from "react"
import { Clock, AlertCircle, CheckCircle2, Loader } from "lucide-react"
import { saveStudentExamAnswers } from "@/app/student/action"
import { toast } from "sonner"

type QuestionType = "input" | "textarea" | "radio" | "checkbox"

interface AnswerOption {
  id: string
  text: string
}

interface Question {
  id: string
  text: string
  type: QuestionType
  required: boolean
  options: AnswerOption[]
}

interface ExamData {
  id: string
  title: string
  description: string
  duration: number
  startDate: string
  startTime: string
  questions: Question[]
}

interface StudentAnswer {
  questionId: string
  answer: string | string[]
}

interface TakeExamProps {
  Data: ExamData,
  studentId:string,
  sessionId:string,
  pauseProctoring : (reason:string) => void,
  resumeProctoring : (reason:string) => void,
  stopProctoring : (reason:string) => void
}

// Mock exam data - in real app, this would come from your database


export default function TakeExam({Data,sessionId,studentId ,pauseProctoring,resumeProctoring,stopProctoring}: TakeExamProps) {
  const [exam, setExam] = useState<ExamData>(Data)
  const [answers, setAnswers] = useState<StudentAnswer[]>([])
  const [timeRemaining, setTimeRemaining] = useState<number>(0)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [showAllQuestions, setShowAllQuestions] = useState(true)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [loading,setLoading] = useState(false)
  let examId='exam-1'

  // set the time reamining based on the duration of the exam
useEffect(() => {
  console.log("Exam start time:", exam.startTime)
  const [hour, minute] = exam.startTime.split(':')
  const totalStartMinutes = Number(hour) * 60 + Number(minute)

  const now = new Date()
  const nowMinutes = now.getHours() * 60 + now.getMinutes()

  const elapsedMinutes = nowMinutes - totalStartMinutes
  const remainingMinutes = exam.duration - elapsedMinutes
  console.log(remainingMinutes)
  // If remainingMinutes is negative, the exam time is over
  setTimeRemaining(remainingMinutes * 60)
}, [exam.startTime, exam.duration])

  // Timer countdown
  useEffect(() => {
    console.log("Time remaining:", timeRemaining)
    if (timeRemaining > 0 && !isSubmitted) {
      const timer = setTimeout(() => {
        setTimeRemaining(timeRemaining - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (timeRemaining === 0 && !isSubmitted) {
      // handleAutoSubmit()
    }
  }, [timeRemaining, isSubmitted])

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  const getAnswer = (questionId: string): string | string[] => {
    const answer = answers.find((a) => a.questionId === questionId)
    return answer?.answer || ""
  }

  const updateAnswer = (questionId: string, answer: string | string[]) => {
    setAnswers((prev) => {
      const existing = prev.find((a) => a.questionId === questionId)
      if (existing) {
        return prev.map((a) => (a.questionId === questionId ? { ...a, answer } : a))
      } else {
        return [...prev, { questionId, answer }]
      }
    })

    // Clear validation errors for this question
    setValidationErrors((prev) => prev.filter((error) => !error.includes(questionId)))
  }

  const handleCheckboxChange = (questionId: string, optionId: string, checked: boolean) => {
    const currentAnswers = (getAnswer(questionId) as string[]) || []
    let newAnswers: string[]

    if (checked) {
      newAnswers = [...currentAnswers, optionId]
    } else {
      newAnswers = currentAnswers.filter((id) => id !== optionId)
    }

    updateAnswer(questionId, newAnswers)
  }

  const validateAnswers = (): boolean => {
    if (!exam) return false

    const errors: string[] = []

    exam.questions.forEach((question) => {
      if (question.required) {
        const answer = getAnswer(question.id)
        if (
          !answer ||
          (Array.isArray(answer) && answer.length === 0) ||
          (typeof answer === "string" && answer.trim() === "")
        ) {
          errors.push(`Question "${question.text}" is required`)
        }
      }
    })

    setValidationErrors(errors)
    return errors.length === 0
  }

  const handleSubmit =async () => {
    if (!validateAnswers()) {
      return
    }
    setLoading(true);

      pauseProctoring('Exam submission in progress');

    const res = await saveStudentExamAnswers(studentId, sessionId, answers, exam.duration * 60)
    if (!res.success) {
      toast.error("Failed to submit exam. Please try again later.")
      // Resume proctoring if submission failed
      resumeProctoring('Submission failed - proctoring resumed');
      setLoading(false)
      return
    }
    toast.success("Exam submitted successfully!")
    setIsSubmitted(true)
    stopProctoring('Submission successful - proctoring stopped');
  }

  const handleAutoSubmit =async () => {
    pauseProctoring("Auto-submitted due to time limit")
    setLoading(true)
    const res = await saveStudentExamAnswers(studentId, sessionId, answers, exam.duration * 60)
    if (!res.success) {
      toast.error("Failed to submit exam. Please try again later.")
      // Resume proctoring if submission failed
      resumeProctoring('Submission failed - proctoring resumed');
      setLoading(false)
      return
    }
    toast.success("Exam submitted successfully!")
    setIsSubmitted(true)
  }

  const getProgress = (): number => {
    if (!exam) return 0
    const answeredQuestions = exam.questions.filter((q) => {
      const answer = getAnswer(q.id)
      return answer && (typeof answer === "string" ? answer.trim() !== "" : answer.length > 0)
    }).length
    return (answeredQuestions / exam.questions.length) * 100
  }

  const renderQuestion = (question: Question, index: number) => {
    const answer = getAnswer(question.id)

    return (
      <div key={question.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm mb-8">
        <div className="p-8">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-green-100 border border-green-200 rounded-md flex items-center justify-center font-bold text-green-700 text-lg">
                  {index + 1}
                </div>
                <h3 className="text-xl font-bold text-gray-900">
                  Question {index + 1}
                  {question.required && <span className="text-red-500 ml-2">*</span>}
                </h3>
              </div>
              <p className="text-lg text-gray-900  leading-relaxed">{question.text}</p>
            </div>
            <div className="text-sm text-gray-500 bg-gray-50 px-4 py-2 rounded-full border border-gray-200">
              {question.type === "radio"
                ? "Single Choice"
                : question.type === "checkbox"
                  ? "Multiple Choice"
                  : question.type === "textarea"
                    ? "Long Answer"
                    : "Short Answer"}
            </div>
          </div>

          <div className="space-y-2">
            {question.type === "input" && (
              <input
                type="text"
                value={answer as string}
                onChange={(e) => updateAnswer(question.id, e.target.value)}
                placeholder="Enter your answer..."
                disabled={isSubmitted}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 placeholder-gray-400 text-lg disabled:bg-gray-50 disabled:cursor-not-allowed"
              />
            )}

            {question.type === "textarea" && (
              <textarea
                value={answer as string}
                onChange={(e) => updateAnswer(question.id, e.target.value)}
                placeholder="Enter your detailed answer..."
                rows={5}
                disabled={isSubmitted}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 text-gray-900 placeholder-gray-400 resize-none text-lg disabled:bg-gray-50 disabled:cursor-not-allowed"
              />
            )}

            {question.type === "radio" && (
              <div className="space-y-2">
                {question.options.map((option) => (
                  <label
                    key={option.id}
                    className={`flex items-center gap-4 px-4 py-2 rounded-lg cursor-pointer transition-all duration-200 ${
                      (answer as string) === option.id
                        ? "border-green-500 bg-green-50"
                        : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                    } ${isSubmitted ? "cursor-not-allowed opacity-60" : ""}`}
                  >
                    <input
                      type="radio"
                      name={question.id}
                      value={option.id}
                      checked={(answer as string) === option.id}
                      onChange={(e) => updateAnswer(question.id, e.target.value)}
                      disabled={isSubmitted}
                      className="w-4 h-4 text-green-600 border-gray-300 focus:ring-green-500"
                    />
                    <span className="text-lg text-gray-900">{option.text}</span>
                  </label>
                ))}
              </div>
            )}

            {question.type === "checkbox" && (
              <div className="space-y-2">
                {question.options.map((option) => {
                  const isChecked = ((answer as string[]) || []).includes(option.id)
                  return (
                    <label
                      key={option.id}
                      className={`flex items-center gap-4 px-4 py-2  rounded-lg cursor-pointer transition-all duration-200 ${
                        isChecked
                          ? "border-green-500 bg-green-50"
                          : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                      } ${isSubmitted ? "cursor-not-allowed opacity-60" : ""}`}
                    >
                      <input
                        type="checkbox"
                        checked={isChecked}
                        onChange={(e) => handleCheckboxChange(question.id, option.id, e.target.checked)}
                        disabled={isSubmitted}
                        className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                      />
                      <span className="text-lg text-gray-900">{option.text}</span>
                    </label>
                  )
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  if (!exam) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-6">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-12 text-center max-w-md w-full">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-green-500 border-t-transparent mx-auto mb-6"></div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">Loading Exam</h2>
          <p className="text-gray-500">Please wait while we prepare your exam...</p>
        </div>
      </div>
    )
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-6">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-12 text-center max-w-2xl w-full">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle2 className="w-10 h-10 text-green-600" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Exam Submitted Successfully!</h2>
          <p className="text-lg text-gray-600 mb-6">
            Thank you for completing the exam. Your answers have been recorded and will be reviewed.
          </p>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <div className="grid grid-cols-2 gap-6 text-center">
              <div>
                <div className="text-2xl font-bold text-gray-900">{exam.questions.length}</div>
                <div className="text-sm text-gray-500">Questions Answered</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{formatTime(exam.duration * 60 - timeRemaining)}</div>
                <div className="text-sm text-gray-500">Time Spent</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto p-6 space-y-8">
        {/* Exam Header */}
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
          <div className="p-8">
            <div className="flex items-start justify-between mb-6">
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900 mb-3">{exam.title}</h1>
                <p className="text-lg text-gray-600 leading-relaxed">{exam.description}</p>
              </div>
              <div className="text-right">
                <div
                  className={`flex items-center gap-3 text-2xl font-mono font-bold mb-2 ${
                    timeRemaining < 300 ? "text-red-600" : "text-gray-900"
                  }`}
                >
                  <Clock className="w-6 h-6" />
                  {formatTime(timeRemaining)}
                </div>
                <div className="text-sm text-gray-500">Duration: {exam.duration} minutes</div>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="font-semibold text-gray-900">Progress</span>
                <span className="text-gray-600">{Math.round(getProgress())}% Complete</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-green-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${getProgress()}%` }}
                ></div>
              </div>
            </div>

            {/* Time Warning */}
            {timeRemaining < 300 && (
              <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
                <div>
                  <div className="font-semibold text-red-800">Time Warning</div>
                  <div className="text-red-700">Less than 5 minutes remaining! Please review your answers.</div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <div className="font-semibold text-red-800 mb-3">Please complete the following required fields:</div>
                <ul className="space-y-2">
                  {validationErrors.map((error, index) => (
                    <li key={index} className="text-red-700 flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-red-600 rounded-full"></div>
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Questions */}
        <div className="space-y-8">
          {showAllQuestions
            ? exam.questions.map((question, index) => renderQuestion(question, index))
            : renderQuestion(exam.questions[currentQuestion], currentQuestion)}
        </div>

        {/* Navigation and Submit */}
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
          <div className="p-8">
            <div className="flex items-center justify-between">
              <div className="text-gray-600">
                <span className="font-semibold text-gray-900">{answers.length}</span> of{" "}
                <span className="font-semibold text-gray-900">{exam.questions.length}</span> questions answered
              </div>

              <div className="flex gap-4">
                {!showAllQuestions && (
                  <>
                    <button
                      onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
                      disabled={currentQuestion === 0}
                      className="px-6 py-3 border border-gray-200 rounded-md font-semibold text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => setCurrentQuestion(Math.min(exam.questions.length - 1, currentQuestion + 1))}
                      disabled={currentQuestion === exam.questions.length - 1}
                      className="px-6 py-3 border border-gray-200 rounded-md font-semibold text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      Next
                    </button>
                  </>
                )}

                <button
                  onClick={handleSubmit}
                  disabled={loading}
                  className="bg-green-600 disabled:opacity-50 hover:bg-green-700 text-white font-semibold px-8 py-3 rounded-md transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2"
                >
                 {loading ? <Loader className="size-5 animate-spin duration-300 mr-2"/> : <CheckCircle2 className="w-5 h-5" />}
                  Submit Exam
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
